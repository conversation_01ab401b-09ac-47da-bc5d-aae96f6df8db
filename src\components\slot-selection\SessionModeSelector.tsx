"use client";

import { SessionMode } from "@/types/slot-selection.types";

interface SessionModeSelectorProps {
  sessionModes: SessionMode[];
  onSessionModeSelect: (index: number) => void;
  isMobile?: boolean;
}

export default function SessionModeSelector({
  sessionModes,
  onSessionModeSelect,
  isMobile = false,
}: SessionModeSelectorProps) {
  return (
    <div className={isMobile ? "mb-6" : "mb-6 mx-4"}>
      {!isMobile ? (
        <div className="flex gap-8 mb-4">
          <div className="flex flex-col">
            <h2 className="text-gray-500 text-xs mb-1">STEP 01</h2>
            <h3 className="text-[#251D5C] font-semibold mb-4 gilmer-bold">
              Select Session Mode
            </h3>
          </div>
          <div className="flex flex-col gap-4">
            {sessionModes.map((mode, index) => (
              <div
                key={index}
                className={`rounded-xl py-2 px-5 text-sm text-[#251D5C] font-medium cursor-pointer border transition-colors
                  ${
                    mode.selected
                      ? "border-[#718FFF] bg-[#718FFF]/10"
                      : "border-gray-200 hover:border-[#718FFF] hover:bg-[#718FFF]/5"
                  }`}
                onClick={() => onSessionModeSelect(index)}
              >
                {mode.name}
              </div>
            ))}
          </div>
        </div>
      ) : (
        <>
          <h3 className="text-[#251D5C] font-semibold mb-3 gilmer-bold">
            Select Session Mode
          </h3>
          <div className="flex flex-col gap-2">
            {sessionModes.map((mode, index) => (
              <div
                key={index}
                className={`rounded-xl py-2 px-4 text-sm text-[#251D5C] font-medium cursor-pointer border transition-colors
                  ${
                    mode.selected
                      ? "border-[#718FFF] bg-[#718FFF]/10"
                      : "border-gray-200 hover:border-[#718FFF] hover:bg-[#718FFF]/5"
                  }`}
                onClick={() => onSessionModeSelect(index)}
              >
                {mode.name}
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
