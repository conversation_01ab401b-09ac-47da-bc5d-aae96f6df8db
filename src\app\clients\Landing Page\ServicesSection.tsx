import TherapyCard from '@/components/client/TherapyCard'

export default function ClientServicesSection() {
  return (
    <section id="services" className="mb-6 lg:mb-12 pt-6 lg:pt-12">
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-8">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4 font-new-elegance">
            Thought Pudding <span className="text-[#6E58A5] text-5xl font-bold">Services</span>
          </h2>
        </div>
        
        {/* Therapy Cards */}
        <div className="flex flex-col md:flex-row justify-center items-center gap-16">
          <TherapyCard 
            title="Individual & Couples Therapy"
            cardTitle="Weekly 1:1 sessions. Slowly, deeply, steadily."
            cardDescription="Psychotherapy for your concerns - we hear it all"
            tagText="Flexible formats: Online or in-person"
            svgPath="/assets/images/client/individual_and_couples_therapy.svg"
            outerBgColor="bg-[#6E58A5]"
            innerBgColor="bg-[#5A4689]"
          />
          <TherapyCard 
            title="Mental Health Assessments"
            cardTitle="Sometimes, it helps to name it."
            cardDescription={`• ADHD, Depression, Anxiety, Autism and Personality assessments only
• Conducted by RCI-licensed clinical psychologists`}
            tagText="Flexible formats: Online or in-person"
            svgPath="/assets/images/client/mental_health_assessment.svg"
            svgSize={{ width: 280, height: 200 }}
            svgPosition={{ top: 130, left: 0 }}
            innerCardSize={{ width: 300, height: 240 }}
            outerBgColor="bg-[#FF9CC7]"
            innerBgColor="bg-[#F882B5]"
          />
        </div>
      </div>
    </section>
  );
} 