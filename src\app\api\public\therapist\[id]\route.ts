import { NextRequest, NextResponse } from 'next/server';
import { fetchTherapistProfileById } from '@/services/public-calendar.service';

/**
 * GET handler for fetching therapist profile by ID
 * @param request - The incoming request
 * @param params - Route parameters including the therapist ID
 * @returns NextResponse with therapist data or error
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        {
          status: 'error',
          message: 'Therapist ID is required',
          errors: [{ code: 'MISSING_ID', message: 'Therapist ID is required' }],
          responseCode: 400,
        },
        { status: 400 }
      );
    }

    // Use the existing service function to fetch therapist data
    const therapistData = await fetchTherapistProfileById(id);

    return NextResponse.json(therapistData);
  } catch (error) {
    console.error('Error fetching therapist profile:', error);
    
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to fetch therapist profile',
        errors: [{ code: 'SERVER_ERROR', message: 'An unexpected error occurred' }],
        responseCode: 500,
      },
      { status: 500 }
    );
  }
}
