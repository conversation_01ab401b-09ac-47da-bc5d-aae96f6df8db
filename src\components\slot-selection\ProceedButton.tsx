"use client";

import { SessionMode } from "@/types/slot-selection.types";
import { WorkingHoursSlot } from "@/services/public-calendar.service";

interface ProceedButtonProps {
  sessionModes: SessionMode[];
  selectedDate: Date | null;
  selectedTimeSlot: WorkingHoursSlot | null;
  onProceed: () => void;
  isMobile?: boolean;
}

export default function ProceedButton({
  sessionModes,
  selectedDate,
  selectedTimeSlot,
  onProceed,
  isMobile = false,
}: ProceedButtonProps) {
  const isDisabled = !sessionModes.some((mode) => mode.selected) || !selectedDate || !selectedTimeSlot;

  if (isMobile) {
    return (
      <button
        className="w-full bg-[#2C58BB] text-white py-3 rounded-lg font-medium hover:bg-[#718FFF] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        onClick={onProceed}
        disabled={isDisabled}
      >
        Proceed
      </button>
    );
  }

  return (
    <div className="flex justify-end mt-8">
      <button
        className="bg-[#2C58BB] text-white px-8 py-3 rounded-lg font-medium hover:bg-[#718FFF] transition-colors text-base disabled:opacity-50 disabled:cursor-not-allowed"
        onClick={onProceed}
        disabled={isDisabled}
      >
        Proceed
      </button>
    </div>
  );
}
