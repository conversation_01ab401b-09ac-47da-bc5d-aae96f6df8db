import HowItWorksCard from "@/components/client/HowItWorksCard";
import BeginTherapyButton from "@/components/client/BeginTherapyButton";

const howItWorksData = [
  {
    icon: "/assets/images/client/how-it-works-card1-icon.svg",
    title: "First Time Therapy Guidance",
    description: "We're here to help, wherever you are in your therapy journey"
  },
  {
    icon: "/assets/images/client/how-it-works-card2-icon.svg",
    title: "Get thoughtful recommendations",
    description: "We match you based on real human therapeutic insights"
  },
  {
    icon: "/assets/images/client/how-it-works-card3-icon.svg",
    title: "Book your first session",
    description: "Start with a qualified therapist who offers lasting care"
  }
];

export default function ClientHowItWorksSection() {
  return (
    <section id="how-it-works" className="relative w-screen overflow-hidden -mx-4 lg:-mx-[150px] md:-mx-[75px]">
      {/* PNG Background */}
      <div 
        className="absolute inset-0 w-full h-full bg-no-repeat bg-center"
        style={{
          backgroundImage: 'url(/assets/images/client/how-it-works-bg.png)',
          backgroundSize: '100% 100%',
          minHeight: '100px'
        }}
      />

      {/* Mobile Layout */}
      <div className="lg:hidden relative min-h-[1px]">
        <div className="relative z-10 pt-4 pb-8">
          <div className="max-w-6xl mx-auto px-4 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-8 font-new-elegance">
              How it <span className="italic">works</span>
            </h2>
            
            {/* Cards Container - Mobile */}
            <div className="flex flex-col items-center justify-center gap-8 mb-12">
              {howItWorksData.map((card, index) => (
                <HowItWorksCard
                  key={index}
                  icon={card.icon}
                  title={card.title}
                  description={card.description}
                />
              ))}
            </div>
            
            {/* Begin Therapy Button with Line - Mobile */}
            <div className="relative flex flex-col items-center">
              <svg 
                width="100%" 
                height="3" 
                viewBox="0 0 892 3" 
                fill="none" 
                xmlns="http://www.w3.org/2000/svg"
                className="absolute top-1/2 -translate-y-1/2 z-0"
              >
                <line 
                  x1="0" 
                  y1="1.65869" 
                  x2="100%" 
                  y2="1.65877" 
                  stroke="white" 
                  strokeWidth="2"
                />
              </svg>
              <BeginTherapyButton />
            </div>
          </div>
        </div>
        {/* Mobile Bottom Curve */}
        <div className="absolute bottom-0 left-0 right-0 h-8 bg-white" style={{
          clipPath: "ellipse(50% 100% at 50% 100%)"
        }} />
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block relative min-h-[1000px]">
        <div className="relative z-10 pt-4 pb-8">
          <div className="max-w-6xl mx-auto px-4 text-center">
            <h2 className="text-6xl font-bold mb-12 font-new-elegance">
              How it <span className="italic">works</span>
            </h2>
            
            {/* Cards Container - Desktop */}
            <div className="flex flex-row items-center justify-center gap-20 mb-16">
              {howItWorksData.map((card, index) => (
                <HowItWorksCard
                  key={index}
                  icon={card.icon}
                  title={card.title}
                  description={card.description}
                />
              ))}
            </div>
            
            {/* Begin Therapy Button with Line - Desktop */}
            <div className="relative flex flex-col items-center">
              <svg 
                width="892" 
                height="3" 
                viewBox="0 0 892 3" 
                fill="none" 
                xmlns="http://www.w3.org/2000/svg"
                className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-[892px] z-0"
              >
                <line 
                  x1="8.74228e-08" 
                  y1="1.65869" 
                  x2="892" 
                  y2="1.65877" 
                  stroke="white" 
                  strokeWidth="2"
                />
              </svg>
              <BeginTherapyButton />
            </div>
          </div>
        </div>
        {/* Desktop Bottom Curve */}
        <div className="absolute bottom-0 left-0 right-0 h-8 bg-white" style={{
          clipPath: "ellipse(50% 100% at 50% 100%)"
        }} />
      </div>
    </section>
  );
} 