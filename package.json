{"name": "thought-pudding", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "tsc && next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next/font": "^14.2.15", "@phosphor-icons/react": "^2.1.7", "axios": "^1.7.7", "chart.js": "^4.5.0", "chartjs-plugin-datalabels": "^2.2.0", "date-fns": "^4.1.0", "formik": "^2.4.6", "moment": "^2.30.1", "next": "14.2.15", "nprogress": "^0.2.0", "react": "^18", "react-chartjs-2": "^5.2.0", "react-dom": "^18", "react-flatpickr": "^3.10.13", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-phone-number-input": "^3.4.12", "sharp": "^0.34.2", "swiper": "^11.1.12", "swr": "^2.2.5", "usetiful-sdk": "^0.1.3", "yup": "^1.4.0"}, "devDependencies": {"@types/chart.js": "^2.9.41", "@types/date-fns": "^2.5.3", "@types/node": "^20", "@types/nprogress": "^0.2.3", "@types/react": "^18.3.23", "@types/react-dom": "^18", "@types/react-flatpickr": "^3.8.11", "@types/sharp": "^0.31.1", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}