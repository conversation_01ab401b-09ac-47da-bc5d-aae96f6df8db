import { Roboto, Caveat } from "next/font/google";
import { useState } from "react";
import { TherapistData } from "./types/therapist";
import { getCarouselTherapists, generateTherapistUrl } from "./utils/therapists";

const roboto = Roboto({ subsets: ["latin"], weight: "700" });
const caveat = Caveat({ subsets: ["latin"], weight: "400" });

interface TherapistCardProps {
  therapist: TherapistData;
}

function TherapistCard({ therapist }: TherapistCardProps) {
  const accentColors = {
    pink: {
      accent: '#FF9CC7',
      background: '#FDF3DD'
    },
    purple: {
      accent: '#7058A5', 
      background: '#FDF3DD'
    }
  };

  const colors = accentColors[therapist.accentColor];

  return (
    <div 
      className="relative w-[289px] h-[388px] mx-auto transition-transform hover:scale-105 hover:z-10"
    >
      {/* Main Card Background */}
      <div 
        className="absolute inset-0 rounded-[20px] shadow-lg"
        style={{ backgroundColor: colors.background }}
      />
      
      {/* Decorative Accent Shape */}
      <div 
        className="absolute w-[185px] h-[188px] rounded-full left-1/2 transform -translate-x-1/2"
        style={{ 
          backgroundColor: colors.accent,
          top: '120px'
        }}
      />
      
      {/* Photo Container with Mask */}
      <div className="absolute top-[119px] left-1/2 transform -translate-x-1/2 w-[220px] h-[270px] overflow-hidden rounded-[20px] bg-[#FFFDD0]">
        <img 
          src={therapist.image}
          alt={therapist.name}
          className="w-[140%] h-[140%] object-cover object-center -translate-y-[15%]"
        />
      </div>
      
      {/* Content Container */}
      <div className="absolute top-[31px] left-1/2 transform -translate-x-1/2 flex flex-col items-center">
        {/* Name and Title */}
        <div className="text-center mb-4 w-[250px]">
          <h3 className={`text-[24px] font-bold text-black mb-1 ${caveat.className} whitespace-nowrap`} style={{ textTransform: 'capitalize' }}>
            {therapist.name}
          </h3>
          <p className="text-[12px] font-normal text-[#252525] font-roboto leading-[1.22]">
            {therapist.title}
          </p>
        </div>
        
        {/* Experience Badge */}
        <div className="bg-[#FFFEF0] px-2 py-1.5 rounded-lg mb-8">
          <span className="text-[12px] font-medium text-black font-roboto">
            {therapist.experience}
          </span>
        </div>
      </div>
      
      {/* Know More Button */}
      <div className="absolute bottom-[20px] left-1/2 transform -translate-x-1/2">
        <a 
          href={generateTherapistUrl(therapist)}
          className="bg-[#E9FA6F] rounded-[18px] px-5 py-3 flex items-center gap-3 hover:bg-[#d4e559] transition-colors inline-flex whitespace-nowrap"
        >
          <span className="text-[16px] font-medium text-black font-roboto">
            Know More
          </span>
          <div className="w-[28px] h-[28px] bg-black rounded-[13px] flex items-center justify-center">
            <svg width="9" height="7" viewBox="0 0 9 7" fill="none">
              <path 
                d="M1 3.5L7.5 3.5M7.5 3.5L5 1M7.5 3.5L5 6" 
                stroke="#FFFEF0" 
                strokeWidth="1.4" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </a>
      </div>
    </div>
  );
}

export default function ClientWhoWeAreSection() {
  // Load therapist data from JSON
  const therapists = getCarouselTherapists();

  // Carousel state
  const [currentIndex, setCurrentIndex] = useState(0);
  const cardsPerView = 3;

  // Removed auto-advance functionality - user controls navigation manually

  // Navigation functions
  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % therapists.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + therapists.length) % therapists.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  // Get current visible therapists
  const getCurrentTherapists = () => {
    const startIndex = currentIndex * cardsPerView;
    return therapists.slice(startIndex, startIndex + cardsPerView);
  };

      return (
    <section id="about" className="relative w-screen overflow-hidden -mx-4 lg:-mx-[150px] md:-mx-[75px] py-2 lg:py-4 pb-16 lg:pb-20 bg-[#7058A5]">
      {/* Top Parabolic Curve */}
      <div
        className="absolute top-0 left-0 w-full h-8 bg-white"
        style={{
          clipPath: "ellipse(50% 100% at 50% 0%)"
        }}
      />
      
      <div className="max-w-7xl mx-auto px-4 pt-2">
        {/* Mobile Layout - Vertical Stack */}
        <div className="flex flex-col items-center gap-12 lg:hidden">
          {/* Text Content - Mobile */}
          <div className="text-center mt-4">
            <div className="text-white">
              <h2 className={`text-3xl md:text-4xl font-bold mt-4 mb-4 ${roboto.className}`}>
                Who <span className="italic">we are</span>
              </h2>
              <div className="space-y-4 max-w-md mx-auto">
                <p className="text-base font-roboto italic">
                  We&apos;re therapists who&apos;ve seen it firsthand—some therapy seekers start confused, some leave disappointed.
                </p>
                <p className="text-base font-roboto">
                  That&apos;s why we built Thought Pudding- to take guess work out of starting therapy.
                </p>
              </div>
            </div>
          </div>

          {/* Therapist Cards Carousel - Mobile */}
          <div className="w-full">
            {/* Carousel Container */}
            <div className="relative w-full">
                             <div className="flex items-center justify-center gap-4 transition-transform duration-500 ease-in-out px-4 py-8">
                 <div className="relative">
                   <TherapistCard therapist={therapists[currentIndex]} />
                 </div>
               </div>
            </div>

            {/* Navigation Controls - Mobile */}
            <div className="w-full flex justify-center mt-4 mb-4">
              <div className="flex items-center gap-4">
                {/* Previous Button */}
                <button
                  onClick={prevSlide}
                  className="w-10 h-10 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full flex items-center justify-center transition-all duration-200"
                  aria-label="Previous therapists"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path 
                      d="M15 18L9 12L15 6" 
                      stroke="white" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>

                {/* Dot Indicators */}
                <div className="flex gap-2">
                  {Array.from({ length: therapists.length }).map((_, index) => (
                    <button
                      key={index}
                      onClick={() => goToSlide(index)}
                      className={`w-2 h-2 rounded-full transition-all duration-200 ${
                        index === currentIndex 
                          ? 'bg-[#E9FA6F]' 
                          : 'bg-white bg-opacity-40'
                      }`}
                      aria-label={`Go to slide ${index + 1}`}
                    />
                  ))}
                </div>

                {/* Next Button */}
                <button
                  onClick={nextSlide}
                  className="w-10 h-10 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full flex items-center justify-center transition-all duration-200"
                  aria-label="Next therapists"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path 
                      d="M9 18L15 12L9 6" 
                      stroke="white" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Layout - Horizontal */}
        <div className="hidden lg:flex items-center justify-between min-h-[500px] gap-12">
          {/* Left Side - Text Content */}
          <div className="flex-1 max-w-lg mt-4">
            <div className="text-white">
              <h2 className={`text-4xl md:text-5xl font-bold mb-4 ${roboto.className}`}>
                Who <span className="italic">we are</span>
              </h2>
              <div className="space-y-4">
                <p className="text-lg font-roboto italic">
                  We&apos;re therapists who&apos;ve seen it firsthand—some therapy seekers start confused, some leave disappointed.
                </p>
                <p className="text-lg font-roboto">
                  That&apos;s why we built Thought Pudding- <br />
                  to take guess work out of starting therapy.
                </p>
              </div>
            </div>
          </div>

          {/* Right Side - Therapist Cards Carousel */}
          <div className="flex-1 flex flex-col items-end">
            {/* Carousel Container */}
            <div className="relative w-full max-w-4xl">
              <div className="flex items-center justify-center gap-16 transition-transform duration-500 ease-in-out px-12 py-16">
                {getCurrentTherapists().map((therapist, index) => (
                  <div
                    key={therapist.id}
                    className="relative"
                    style={{
                      zIndex: getCurrentTherapists().length - index, // First card on top
                      marginLeft: index > 0 ? '-30px' : '0', // Reduced overlap for more space
                    }}
                  >
                    <TherapistCard therapist={therapist} />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Bottom Parabolic Curve */}
      <div 
        className="absolute bottom-0 left-0 w-full h-16 bg-white"
        style={{
          clipPath: "ellipse(50% 100% at 50% 100%)"
        }}
      />
    </section>
  );
} 