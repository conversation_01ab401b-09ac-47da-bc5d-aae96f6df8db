// src/context/AuthContext.js
"use client";

import { createContext, useContext, useState, useEffect } from "react";

const AuthContext = createContext<
  | {
      isAuthenticated: boolean;
      setIsAuthenticated: React.Dispatch<React.SetStateAction<boolean>>;
      loading: boolean;
      isRedirecting: boolean;
      setIsRedirecting: React.Dispatch<React.SetStateAction<boolean>>;
    }
  | undefined
>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [isRedirecting, setIsRedirecting] = useState<boolean>(false);

  useEffect(() => {
    if (typeof localStorage !== "undefined") {
      // Simulate fetching authentication state
      const token = localStorage.getItem("authKeyTh");
      setIsAuthenticated(!!token);
      setLoading(false);
    }
  }, []);

  return (
    <AuthContext.Provider
      value={{ isAuthenticated, setIsAuthenticated, loading, isRedirecting, setIsRedirecting }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
