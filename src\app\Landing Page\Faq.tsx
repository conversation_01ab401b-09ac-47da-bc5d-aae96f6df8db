import React, { useState } from "react";
import { MdOutlineKeyboardArrowDown } from "react-icons/md";

type FAQ = {
  question: string;
  answer: string | JSX.Element;
};

const faqData: FAQ[] = [
  {
    question: "Who can use the platform?",
    answer:
      "Our platform is built to support all psychotherapists in private practice, whether youʼre a counseling psychologist, clinical psychologist, or psychotherapist. Whether youʼre just starting, a part time practitioner to those of you managing a full client load, weʼre here for every stage of your journey.",
  },
  {
    question: "What is the pricing?",
    answer:
      "After giving the platform free of use last year, we’ve decided to come out with monthly (500 INR/month) and yearly (1000 INR/year) subscriptions which are affordable for both starting and more established psychotherapists. ",
  },
  {
    question: "How do you ensure confidentiality of data?",
    answer: (
      <>
        Any and all client data that is used in your private practice is protected with utmost seriousness through our in-house client ID system. For more, you can view our detailed policy{" "}
        <a
          href="/privacy-policy"
          target="_blank"
          rel="noopener noreferrer"
          className="text-[#514ED8] underline hover:text-[#3b38b2] transition"
        >
          here
        </a>.
      </>
    ),
  },
  {
    question: "What is your verification process?",
    answer:
      "We verify every therapist individually and go through their degree, qualification and license if required before approving access to the platform. In case you have a collective or clinic, the same process applies. We process your verification within 48 hours of your signing up via email. ",
  },
];

type AccordionItemProps = {
  question: string;
  answer: string | JSX.Element;
  isOpen: boolean;
  onClick: () => void;
};

const AccordionItem: React.FC<AccordionItemProps> = ({
  question,
  answer,
  isOpen,
  onClick,
}) => (
  <div className="bg-[#F5F5F5] rounded-md mb-4 overflow-hidden transition-all duration-300 shadow-sm">
    <button
      onClick={onClick}
      className="flex justify-between items-center text-[14px] sm:text-[18px] lg:text-[22px] w-full px-4 py-4 text-left font-medium text-[#2B2B2B]"
    >
      {question}
      <MdOutlineKeyboardArrowDown
        className={`text-[20px] sm:text-[24px] transition-transform duration-300 ease-in-out ${
          isOpen ? "rotate-180" : ""
        }`}
      />
    </button>

    <div
      className={`px-4 transition-all duration-300 ease-in-out text-[#4B5563] leading-relaxed ${
        isOpen
          ? "max-h-[500px] py-3 text-[10px] sm:text-[14px] lg:text-[18px] opacity-100"
          : "max-h-0 opacity-0 overflow-hidden"
      }`}
    >
      {answer}
    </div>
  </div>
);


const FaqSection: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  return (
    <section className="w-full py-16 flex flex-col md:flex-row justify-between md:items-center gap-10 md:gap-0">
      {/* Heading Section - center on small screens, left on md+ */}
      <div className="w-full md:w-1/2 flex flex-col items-center md:items-start text-center md:text-left px-4 md:px-0">
        <h2 className="text-5xl font-semibold text-[#718FFF] mb-2">FAQs</h2>
        <p className="text-lg font-medium text-black">
          Objections and common questions
        </p>
      </div>

      {/* Accordion Section - sticks to right */}
      <div className="w-full md:w-1/2 px-4 md:px-0">
        {faqData.map((faq, index) => (
          <AccordionItem
            key={index}
            question={faq.question}
            answer={faq.answer}
            isOpen={openIndex === index}
            onClick={() => setOpenIndex(openIndex === index ? null : index)}
          />
        ))}
      </div>
    </section>
  );
};

export default FaqSection;
