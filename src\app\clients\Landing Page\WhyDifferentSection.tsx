import { Roboto } from "next/font/google";

const roboto = Roboto({ subsets: ["latin"], weight: "700" });

export default function ClientWhyDifferentSection() {
  return (
    <section id="why-different" className="relative w-screen overflow-hidden -mx-4 lg:-mx-[150px] md:-mx-[75px] py-12 bg-[#FDF3DD]">
      {/* Top Parabolic Curve */}
      <div className="absolute top-0 left-0 right-0 h-28 bg-white" style={{
        clipPath: "ellipse(50% 100% at 50% 0%)"
      }} />
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Title */}
        <div className="text-center mb-12 mt-24 lg:mt-28">
          <h2 className={`text-4xl md:text-4xl lg:text-6xl font-bold text-[#6F58A5] ${roboto.className}`}>
            Why <span className="italic">we&apos;re different</span>
          </h2>
        </div>

        {/* Mobile Layout */}
        <div className="lg:hidden">
          <div className="flex flex-col items-center gap-12 px-2 pb-28">
            {/* First Block - Mobile */}
            <div className="flex flex-col items-center text-center max-w-[400px]">
              <div className="w-[250px] h-[240px] mb-6">
                <img 
                  src="/assets/images/client/different_vector_one.png" 
                  alt="Find the right Therapist illustration"
                  className="w-full h-full object-contain"
                />
              </div>
              <h3 className="text-xl md:text-2xl font-semibold text-black mb-4 font-roboto">
                &quot;Find the right Therapist&quot;
              </h3>
              <p className="text-base font-normal text-black font-roboto leading-relaxed">
                Navigating pain is hard enough, so we ensure every therapist is trained to offer deep, lasting emotional support and care.
              </p>
            </div>

            {/* Second Block - Mobile */}
            <div className="flex flex-col items-center text-center max-w-[400px]">
              <div className="w-[200px] h-[220px] mb-6">
                <img 
                  src="/assets/images/client/different_vector_two.png" 
                  alt="Fixed in Two Sessions illustration"
                  className="w-full h-full object-contain"
                />
              </div>
              <h3 className="text-xl md:text-2xl font-semibold text-black mb-4 font-roboto">
                &quot;Fixed in Two Sessions&quot;
              </h3>
              <p className="text-base font-normal text-black font-roboto leading-relaxed">
                Some weeks bring clarity, others just a quiet shift. Over time, it builds real change often takes six months or so.
              </p>
            </div>

            {/* Third Block - Mobile */}
            <div className="flex flex-col items-center text-center max-w-[400px]">
              <div className="w-[220px] h-[200px] mb-6">
                <img 
                  src="/assets/images/client/different_vector_three.png" 
                  alt="Therapy means No sadness ever illustration"
                  className="w-full h-full object-contain"
                />
              </div>
              <h3 className="text-xl md:text-2xl font-medium text-black mb-4 font-roboto">
                &quot;Therapy means No sadness ever&quot;
              </h3>
              <p className="text-base font-normal text-black font-roboto leading-relaxed">
                Therapy that keeps you grounded through life&apos;s ups and downs not magic, just steady, honest change that stays with you.
              </p>
            </div>
          </div>
          {/* Mobile Bottom Curve */}
          <div className="absolute bottom-0 left-0 right-0 h-28 bg-white" style={{
            clipPath: "ellipse(50% 100% at 50% 100%)"
          }} />
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:block">
          <div className="relative min-h-[800px] pb-28">
            {/* First Block - Top Left */}
            <div className="absolute top-0 left-0 flex items-center gap-8 max-w-[800px]">
              {/* Vector 1 */}
              <div className="flex-shrink-0">
                <div className="w-[300px] h-[288px] relative">
                  <img 
                    src="/assets/images/client/different_vector_one.png" 
                    alt="Find the right Therapist illustration"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              
              {/* Text Content 1 */}
              <div className="flex flex-col gap-2 max-w-[447px]">
                <h3 className="text-xl md:text-2xl lg:text-[32px] font-semibold text-black text-center font-roboto leading-[1.17]">
                  &quot;Find the right Therapist&quot;
                </h3>
                <p className="text-sm md:text-base lg:text-[16px] font-normal text-black font-roboto leading-[1.17]">
                  Navigating pain is hard enough, so we ensure every therapist is trained to offer deep, lasting emotional support and care.
                </p>
              </div>
            </div>

            {/* Second Block - Middle Right */}
            <div className="absolute top-[200px] right-0 flex items-center gap-8 max-w-[700px]">
              {/* Text Content 2 */}
              <div className="flex flex-col gap-2 max-w-[445px] text-right">
                <h3 className="text-xl md:text-2xl lg:text-[32px] font-semibold text-black font-roboto leading-[1.17]">
                  &quot;Fixed in Two Sessions&quot;
                </h3>
                <p className="text-sm md:text-base lg:text-[16px] font-normal text-black font-roboto leading-[1.17]">
                  Some weeks bring clarity, others just a quiet shift. Over time, it builds real change often takes six months or so.
                </p>
              </div>
              
              {/* Vector 2 */}
              <div className="flex-shrink-0">
                <div className="w-[227px] h-[249px] relative">
                  <img 
                    src="/assets/images/client/different_vector_two.png" 
                    alt="Fixed in Two Sessions illustration"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
            </div>

            {/* Third Block - Bottom Left */}
            <div className="absolute top-[450px] left-0 flex items-center gap-8 max-w-[800px]">
              {/* Vector 3 */}
              <div className="flex-shrink-0">
                <div className="w-[241px] h-[222px] relative">
                  <img 
                    src="/assets/images/client/different_vector_three.png" 
                    alt="Therapy means No sadness ever illustration"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              
              {/* Text Content 3 */}
              <div className="flex flex-col gap-2 max-w-[513px]">
                <h3 className="text-xl md:text-2xl lg:text-[32px] font-medium text-black font-roboto leading-[1.17]">
                  &quot;Therapy means No sadness ever&quot;
                </h3>
                <p className="text-sm md:text-base lg:text-[16px] font-normal text-black font-roboto leading-[1.17]">
                  Therapy that keeps you grounded through life&apos;s ups and downs not magic, just steady, honest change that stays with you.
                </p>
              </div>
                         </div>
           </div>
           {/* Desktop Bottom Curve */}
           <div className="absolute bottom-0 left-0 right-0 h-28 bg-white" style={{
             clipPath: "ellipse(50% 100% at 50% 100%)"
           }} />
         </div>
       </div>
     </section>
  );
} 