import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { getTherapistBySlug, getAllTherapists } from '@/app/clients/Landing Page/utils/therapists';
import TherapistDetailPage from '@/components/client/TherapistDetailPage';

interface TherapistPageProps {
  params: {
    slug: string;
  };
}

// Generate static params for all therapists
export async function generateStaticParams() {
  const therapists = getAllTherapists();
  
  return therapists.map((therapist) => ({
    slug: therapist.slug,
  }));
}

// Generate metadata for SEO
export async function generateMetadata({ params }: TherapistPageProps): Promise<Metadata> {
  const therapist = getTherapistBySlug(params.slug);
  
  if (!therapist) {
    return {
      title: 'Therapist Not Found | Thought Pudding',
      description: 'The requested therapist profile could not be found.',
    };
  }

  return {
    title: therapist.metaTitle || `${therapist.name} - ${therapist.title} | Thought Pudding`,
    description: therapist.metaDescription || `Book therapy sessions with ${therapist.name}, ${therapist.title}. ${therapist.experience} in mental health care.`,
    openGraph: {
      title: therapist.name,
      description: therapist.therapyDescription,
      images: [therapist.image],
      type: 'profile',
    },
    twitter: {
      card: 'summary_large_image',
      title: therapist.name,
      description: therapist.therapyDescription,
      images: [therapist.image],
    },
  };
}

export default function TherapistPage({ params }: TherapistPageProps) {
  const therapist = getTherapistBySlug(params.slug);
  
  if (!therapist) {
    notFound();
  }

  return <TherapistDetailPage therapist={therapist} />;
} 