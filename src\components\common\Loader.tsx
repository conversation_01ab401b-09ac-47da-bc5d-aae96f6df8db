"use client";
import React from 'react';

interface LoaderProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
  fullScreen?: boolean;
  className?: string;
}

const Loader: React.FC<LoaderProps> = ({ 
  size = 'medium', 
  text = 'Loading...', 
  fullScreen = false,
  className = ''
}) => {
  // Size mappings
  const sizeClasses = {
    small: {
      container: 'h-5 w-5',
      border: 'border-2'
    },
    medium: {
      container: 'h-8 w-8',
      border: 'border-3'
    },
    large: {
      container: 'h-10 w-10',
      border: 'border-4'
    }
  };

  const { container, border } = sizeClasses[size];

  if (fullScreen) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white/50 backdrop-blur-sm z-[9999] flex-col gap-y-3">
        <div className={`${container} rounded-full animate-spin ${border} border-solid border-blue-600 border-t-transparent`}></div>
        {text && <p className="text-base font-medium text-blue-600">{text}</p>}
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="flex items-center">
        <div className={`${container} rounded-full animate-spin ${border} border-solid border-blue-600 border-t-transparent mr-3`}></div>
        {text && <p className="text-base font-medium text-blue-600">{text}</p>}
      </div>
    </div>
  );
};

export default Loader;
