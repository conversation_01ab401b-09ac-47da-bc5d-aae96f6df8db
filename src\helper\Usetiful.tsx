"use client";
import Script from "next/script";
import { useEffect } from "react";
import { loadUsetifulScript } from "usetiful-sdk";

const Usetiful = ({ token }: { token: string }) => {
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Only load the script on the client side
      loadUsetifulScript(token);
    }
  }, [token]);

  return (
      <Script
        type="text/javascript"
        src={`https://cdn.usetiful.com/sdk.js`}
        async
        defer
      ></Script>
  );
};

export default Usetiful;
