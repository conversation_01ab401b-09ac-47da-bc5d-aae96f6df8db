import Image from "next/image";

interface HowItWorksCardProps {
  icon: string;
  title: string;
  description: string;
}

export default function HowItWorksCard({ icon, title, description }: HowItWorksCardProps) {
  return (
    <div className="bg-[#FDF3DD] rounded-2xl flex flex-col items-center justify-center gap-6 px-8 py-8 max-w-[400px]">
      {/* Icon Container */}
      <div className="w-[106px] h-[106px] flex items-center justify-center">
        <Image
          src={icon}
          alt={title}
          width={136}
          height={136}
          className="w-[106px] h-[106px]"
        />
      </div>
      
      {/* Text Content */}
      <div className="flex flex-col gap-3 w-[200px]">
        <h3 className="text-[24px] leading-[1.45] text-black font-bold text-left font-new-elegance">
          <i>{title}</i>
        </h3>
        <p className="text-base lg:text-[16px] leading-[1.17] text-black opacity-80 font-roboto tracking-[-0.022em] text-left">
          {description}
        </p>
      </div>
    </div>
  );
} 