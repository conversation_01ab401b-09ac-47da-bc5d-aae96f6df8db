import {
  createOrderFromSubscription,
  verifyPayment,
} from "@/services/setting.service";
import toast from "react-hot-toast";

declare global {
  interface Window {
    Razorpay: {
      new (options: {
        key: string;
        // amount: number;
        currency: string;
        name: string;
        description?: string;
        order_id: string;
        handler: (response: {
          razorpay_payment_id: string;
          razorpay_order_id: string;
          razorpay_signature: string;
        }) => void;
        prefill: {
          name: string;
          email: string;
          contact?: string;
        };
        notes?: {
          address: string;
        };
        theme: {
          color: string;
        };
      }): {
        open: () => void;
      };
    };
  }
}

async function loadScript(src: string) {
  const script = document.createElement("script");
  script.src = src;
  document.body.appendChild(script);
  try {
    script.onload = () => {};
    return true;
  } catch {
    return false;
  }
}

export async function checkoutRazorpay({
  name,
  email,
  phone_number,
  subscription_name,
  subscription_id,
  reloadSubscriptionDetails,
  setIsActivating
}: {
  name: string;
  email: string;
  phone_number: string;
  subscription_name: string;
  subscription_id: string;
  reloadSubscriptionDetails: () => Promise<void>;
  setIsActivating: (value: boolean) => void;
}) {
  const res = await loadScript("https://checkout.razorpay.com/v1/checkout.js");

  if (!res) {
    toast.error("Razorpay SDK failed to load. Are you online?");
    return;
  }

  const response = await createOrderFromSubscription(subscription_id)
    .then((res) => {
      if (res.status === 200) {
        return res.data.order;
      } else {
        return false;
      }
    })
    .catch((err) => {
      console.log(err);
      // orderDetails.err = true;
      return false;
    });
  if (!response) {
    toast.error("Failed to create Order");
    return false;
  }
  
  const options = {
    key: process.env.RAZORPAY_KEY || "",
    // amount: 115,
    currency: response?.currency,
    name: name,
    description: "",
    // image: { logo },
    order_id: response?.id || "",
    handler: async function (response: {
      razorpay_payment_id: string;
      razorpay_order_id: string;
      razorpay_signature: string;
    }) {
      setIsActivating(true); // Show loader when payment is complete
      
      // Add 3-second delay for consistent experience
      await new Promise((resolve) => setTimeout(resolve, 3000));

      const data = {
        paymentId: response.razorpay_payment_id,
        orderId: response.razorpay_order_id,
        paymentSignature: response.razorpay_signature,
      };

      const result = await verifyPayment(data)
        .then((res) => {
          if (res.status === 200) {
            toast.success(`${subscription_name} successfully activated!`);
            reloadSubscriptionDetails();
            return res.data;
          }
        })
        .catch((err) => {
          console.log(err);
          toast.error(
            err && err.response && err.response.data
              ? err.response.data
              : "Unable to verify Payment"
          );
          return false;
        })
        .finally(() => {
          setIsActivating(false); // Hide loader
        });

      if (!result) {
        toast.error("Unable to Create Subscription");
      }
    },
    prefill: {
      name: name,
      email: email,
      contact: phone_number,
    },
    notes: {
      address: email,
    },
    theme: {
      color: "#61dafb",
    },
  };

  try {
    console.log("options>>", options);
    const paymentObject = new window.Razorpay(options);
    paymentObject.open();
    return new Promise((resolve) => {
      resolve(paymentObject);
    });
  } catch (err) {
    console.log(err);
  }
}
