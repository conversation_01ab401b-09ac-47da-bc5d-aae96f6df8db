// Using The New Elegance font via CSS class

interface BeginTherapyButtonProps {
  onClick?: () => void;
  className?: string;
}

export default function BeginTherapyButton({ onClick, className = "" }: BeginTherapyButtonProps) {
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      // Open email with predefined structure
      const emailUrl = "mailto:<EMAIL>?subject=Ready%20to%20take%20the%20first%20step%2C%20looking%20for%20therapy&body=Hi%2C%0D%0AMy%20name%20is%20%28enter%20name%29%2C%20I%E2%80%99m%20looking%20for%20assessment%20for%20%28enter%20concerns%29";
      window.location.href = emailUrl;
    }
  };

  return (
    <button 
      onClick={handleClick}
      className={`relative z-10 bg-[#E9FA6F] text-black px-12 py-6 rounded-full text-[20px] font-bold hover:bg-[#d4e862] transition-colors flex items-center justify-between w-[295px] font-new-elegance ${className}`}
    >
      <span className="text-left">Begin Therapy</span>
      <img src="/assets/images/client/arrow-right.svg" alt="Arrow" className="w-[1.2em] h-[1.2em] ml-2" />
    </button>
  );
} 