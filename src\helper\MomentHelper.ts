import moment from "moment";

export class MomentHelper {
    static convertToIST(date : string, time: string) {
        const [year, month, day] = date.split('-').map(Number);
        const [hours, minutes] = time.split(':').map(Number);
        const isoDate = new Date(year, month - 1, day, hours, minutes, 11).toISOString();
        return isoDate;
    }
    static getDateFromIST(date: string){
        return moment(date).format('DD MMM YYYY');
    } 
}


export const addMonths = (dateStr: string, months: number) => {
    const [year, month, day] = dateStr.split("-").map(Number);
    const date = new Date(year, month - 1, day); // Create a Date object (month is 0-indexed)
    date.setMonth(date.getMonth() + months);
  
    // Adjust for cases where adding months rolls over to the next month due to day overflow
    if (date.getDate() !== day) {
      date.setDate(0); // Set to the last day of the previous month
    }
  
    return date.toISOString().split("T")[0]; // Format back to "YYYY-MM-DD"
  };