import { useState, useEffect } from "react";

interface TableRow {
  isPresent: boolean;
  session: string;
  email: string;
  clientName: string;
  mobile: number;
  amount: number;
}

interface TableDataProps {
  data: TableRow[];
}

interface CalendarTBodyProps {
  TableData: TableDataProps;
  handleUpdate: () => void;
  handleSync: () => void;
  errors: { [key: string]: string };
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
  setTableData: React.Dispatch<React.SetStateAction<TableRow[]>>;
  isUpdateDisabled: boolean;
}

const CalendarTBody = ({
  TableData,
  errors,
  setErrors,
  setTableData,
  isUpdateDisabled,
}: CalendarTBodyProps) => {
  const [formData, setFormData] = useState<TableRow[]>([]);

  useEffect(() => {
    if (JSON.stringify(formData) !== JSON.stringify(TableData.data)) {
      setFormData(TableData.data);
    }
  }, [TableData.data, formData]);

  const handleInputChange = (
    index: number,
    field: keyof TableRow,
    value: string | number
  ) => {
    const updatedData = [...formData];
    updatedData[index] = { ...updatedData[index], [field]: value };

    setFormData(updatedData);
    setTableData(updatedData);

    setErrors((prevErrors) => {
      const newErrors = { ...prevErrors };

      if (field === "clientName" && value.toString().trim() !== "") {
        delete newErrors[`${index}-clientName`];
      }

      if (field === "amount" && !isNaN(Number(value)) && value !== "") {
        delete newErrors[`${index}-amount`];
      }

      if (field === "mobile") {
        const valStr = value.toString().trim();
        if (valStr === "") {
          // No error if field is empty
          delete newErrors[`${index}-mobile`];
        } else if (!/^\d{10}$/.test(valStr)) {
          newErrors[`${index}-mobile`] = "Mobile number must be 10 digits";
        } else {
          delete newErrors[`${index}-mobile`];
        }
      }

      return newErrors;
    });
  };

  return (
    <tbody>
      {formData.length > 0 ? (
        formData.map((row, rowIndex) => (
          <tr key={rowIndex}>
            <td className="px-4 py-10">{rowIndex + 1}</td>
            <td className="px-4 py-6">{row.session}</td>
            <td className="px-4 py-10">{row.email}</td>

            <td className="px-4 py-2">
              {row.isPresent || isUpdateDisabled ? (
                row.clientName
              ) : (
                <div className="flex flex-col">
                  <input
                    type="text"
                    value={row.clientName || ""}
                    className="border px-2 py-1 h-14 w-full rounded-lg"
                    placeholder="Enter Name"
                    onChange={(e) =>
                      handleInputChange(rowIndex, "clientName", e.target.value)
                    }
                    disabled={isUpdateDisabled}
                  />
                  <span className="text-red-500 text-[12px] min-h-[14px] block">
                    {errors[`${rowIndex}-clientName`] || ""}
                  </span>
                </div>
              )}
            </td>

            <td className="px-4 py-2">
              {row.isPresent || isUpdateDisabled ? (
                row.mobile
              ) : (
                <div>
                  <input
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    value={row.mobile || ""}
                    className="border px-2 py-1 h-14 w-full rounded-lg"
                    placeholder="Enter Number"
                    onChange={(e) =>
                      handleInputChange(rowIndex, "mobile", e.target.value)
                    }
                    disabled={isUpdateDisabled}
                    maxLength={10}
                  />
                  <span className="text-red-500 text-[12px] min-h-[14px] block">
                    {errors[`${rowIndex}-mobile`] || ""}
                  </span>
                </div>
              )}
            </td>

            <td className="px-4 py-2">
              {row.isPresent || isUpdateDisabled ? (
                row.amount
              ) : (
                <div className="flex flex-col">
                  <input
                    type="number"
                    value={row.amount || ""}
                    className="border px-2 py-1 h-14 w-full rounded-lg"
                    placeholder="Enter Amount"
                    onChange={(e) =>
                      handleInputChange(rowIndex, "amount", e.target.value)
                    }
                    disabled={isUpdateDisabled}
                  />
                  <span className="text-red-500 text-[10px] min-h-[14px] block">
                    {errors[`${rowIndex}-amount`] || ""}
                  </span>
                </div>
              )}
            </td>
          </tr>
        ))
      ) : (
        <tr>
          <td colSpan={6} className="px-4 py-2 text-center border">
            No Data Available
          </td>
        </tr>
      )}
    </tbody>
  );
};

export default CalendarTBody;
