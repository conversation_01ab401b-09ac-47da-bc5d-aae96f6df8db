"use client";
import Image from "next/image";
import React, { Fragment, useEffect, useState } from "react";

import Dropdown from "@/components/common/Dropdown";
import { useAllNotifications } from "@/services/notification.service";
import { useGetSettingData } from "@/services/setting.service";
import axiosInstance from "@/utils/axios";
import { CaretDown, List, User, X } from "@phosphor-icons/react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  DashboardIcon,
  NotificationIcon,
  PatientIcon,
  PaymentIcon,
  SessionIcon,
  SettingIcon,
  PublicCalendarIcon,
} from "../../../public/assets/Svgs";
import Button from "@/components/common/Button";
import CommonModal from "@/components/dashboard/CommonModal";

interface NotificationItem {
  message: string;
  updatedAt?: string;
  createdAt: string;
}

interface HeaderProps {
  setIsAuthenticated?: (isAuthenticated: boolean) => void;
}

const Header: React.FC<HeaderProps> = ({ setIsAuthenticated }) => {
  const router = useRouter();
  const pathname = usePathname();

  const [hasShadow, setHasShadow] = useState(false);
  const [profileDrop, setProfileDrop] = useState(false);
  const [notification, setNotification] = useState(false);
  const [profileImage, setProfileImage] = useState(false);
  const [isLogoutModal, setIsLogoutModal] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const { therapistData } = useGetSettingData();

  const { notificationData, notificationLoading } = useAllNotifications();

  const handleLogout = () => {
    localStorage.removeItem("authKeyTh");
    // Check if setIsAuthenticated is defined before calling it
    if (setIsAuthenticated) {
      setIsAuthenticated(false);
    }
    router.push("/");
    axiosInstance.defaults.headers.Authorization = "";
  };

  // Set hasShadow state based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 0) {
        setHasShadow(true);
      } else {
        setHasShadow(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Update form values when therapistData is available
  useEffect(() => {
    if (therapistData) {
      setProfileImage(therapistData?.profilePhoto);
    }
  }, [therapistData]);

  return (
    <Fragment>
      <header className=" sticky top-0 backdrop-blur-sm pt-3 z-[99]">
        <div
          className={`bg-white sm:p-5 p-4 flex items-center justify-between rounded-base  transition-shadow duration-300 ${
            pathname !== "/dashboard" ? "rounded-t-base" : "rounded-base"
          } ${hasShadow ? "shadow-[0px_4px_6.1px_0px_#E5E9FF80]" : ""}`}
        >
          {/* logo  */}
          <Link href="/dashboard" className="sm:max-w-[172px] max-w-[150px]">
            <Image
              src="/assets/images/newHome/Home-logo.png"
              alt="logo"
              width={300}
              height={300}
              className="w-full h-auto object-contain"
            />
          </Link>

          {/* navigation manu */}

          <nav className="hidden lg:block">
            <ul className="flex gap-3">
              <li
                className={`py-[18px] px-2 2xl:px-6 text-base/4 font-medium ${
                  pathname === "/dashboard"
                    ? "text-yellow-600 bg-yellow-100"
                    : "text-gray-500"
                } flex items-center gap-3 rounded-full cursor-pointer`}
              >
                <Link href="/dashboard" className="flex items-center gap-3">
                  <DashboardIcon
                    className="w-6 h-6"
                    pathFillColor={
                      pathname === "/dashboard" ? "#c58843" : "#5E585A"
                    }
                  />
                  Dashboard
                </Link>
              </li>
              <li
                className={`py-[18px] px-2 2xl:px-6 text-base/4 font-medium ${
                  pathname === "/session"
                    ? "text-yellow-600 bg-yellow-100"
                    : "text-gray-500"
                } flex items-center gap-3 rounded-full cursor-pointer`}
              >
                <Link href="/session" className="flex items-center gap-3">
                  <SessionIcon
                    className="w-6 h-6"
                    pathFillColor={
                      pathname === "/session" ? "#c58843" : "#5E585A"
                    }
                  />
                  Sessions
                </Link>
              </li>
              <li
                className={`py-[18px] px-2 2xl:px-6 text-base/4 font-medium ${
                  pathname === "/payment"
                    ? "text-yellow-600 bg-yellow-100"
                    : "text-gray-500"
                } flex items-center gap-3 rounded-full cursor-pointer`}
              >
                <Link href="/payment" className="flex items-center gap-3">
                  <PaymentIcon
                    className="w-6 h-6"
                    pathFillColor={
                      pathname === "/payment" ? "#c58843" : "#5E585A"
                    }
                  />
                  Payments
                </Link>
              </li>
              <li
                className={`py-[18px] px-2 2xl:px-6 text-base/4 font-medium ${
                  pathname === "/patient"
                    ? "text-yellow-600 bg-yellow-100"
                    : "text-gray-500"
                } flex items-center gap-3 rounded-full cursor-pointer`}
              >
                <Link href="/patient" className="flex items-center gap-3">
                  <PatientIcon
                    className="w-6 h-6"
                    pathFillColor={
                      pathname === "/patient" ? "#c58843" : "#5E585A"
                    }
                  />
                  Clients
                </Link>
              </li>
              <li
                className={`py-[18px] px-2 2xl:px-6 text-base/4 font-medium ${
                  pathname === "/public-calendar"
                    ? "text-yellow-600 bg-yellow-100"
                    : "text-gray-500"
                } flex items-center gap-3 rounded-full cursor-pointer`}
              >
                <Link href="/public-calendar" className="flex items-center gap-3">
                  <PublicCalendarIcon
                    className="w-6 h-6"
                    pathFillColor={
                      pathname === "/public-calendar" ? "#c58843" : "#5E585A"
                    }
                  />
                  Public Calendar
                </Link>
              </li>
            </ul>
          </nav>

          {/* profile area */}
          <div className="flex items-center sm:gap-6 gap-3">
            <List
              className="sm:w-7.5 sm:h-7.5 w-5 h-5 cursor-pointer lg:hidden"
              onClick={() => setIsOpen(true)}
            />
            <Link href={"/setting"} className="lg:block hidden">
              <SettingIcon className="sm:w-7.5 sm:h-7.5 w-5 h-5" />
            </Link>
            <div className="relative flex items-center">
              <button onClick={() => setNotification(!notification)}>
                <NotificationIcon className="sm:w-7.5 sm:h-7.5 w-5 h-5" />
              </button>
              <Dropdown
                className={`sm:w-[400px] w-[200px]`}
                isOpen={notification}
                setIsOpen={setNotification}
              >
                <h4 className="p-5 text-base/6 text-primary font-semibold shadow-[0px_2px_4px_0px_#2C58BB1F]">
                  Notifications
                </h4>
                <ul className="px-5 divide-y divide-primary/10">
                  {notificationLoading ? (
                    // Render Skeletons when loading
                    Array.from({ length: 5 }).map((_, index) => (
                      <li key={index} className="flex items-start gap-4 py-5">
                        <div className="min-w-10 h-10 rounded-full bg-green-600/10 text-green-600 flex items-center justify-center">
                          <div className="w-6 h-6 bg-green-300 animate-pulse rounded-full"></div>
                        </div>
                        <div className="max-w-[290px]">
                          <p className="h-4 bg-primary/10 animate-pulse rounded w-3/4 mb-2"></p>
                          <p className="h-4 bg-primary/10 animate-pulse rounded w-1/2"></p>
                        </div>
                      </li>
                    ))
                  ) : notificationData.length === 0 ? (
                    <li className="flex flex-col items-center justify-center pt-16 pb-20">
                      <Image
                        width={1000}
                        height={1000}
                        src="/assets/images/dashboard/not-found-notification.webp"
                        alt="no-data"
                        className="sm:w-[164px] w-20 h-auto"
                      />
                      <p className="sm:text-lg text-base font-semibold pt-5 text-primary text-center">
                        No Notification Yet
                      </p>
                    </li>
                  ) : (
                    // Render Notification Data when not loading
                    (notificationData as NotificationItem[]).map(
                      (item, index) => (
                        <li key={index} className="flex items-start gap-4 py-5">
                          <div className="min-w-10 h-10 rounded-full bg-green-600/10 text-green-600 flex items-center justify-center">
                            <User size={24} />
                          </div>
                          <div className="max-w-[290px]">
                            <p className="text-sm/5 text-primary font-medium line-clamp-2">
                              {(item as { message: string }).message}
                            </p>
                            <p className="pt-2 text-xs_18 text-primary/50">
                              {item.updatedAt
                                ? `Updated at: ${new Date(
                                    item.updatedAt
                                  ).toLocaleDateString([], {
                                    month: "short",
                                    day: "2-digit",
                                    year: "numeric",
                                  })}, ${new Date(
                                    item.updatedAt
                                  ).toLocaleTimeString([], {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    hour12: true,
                                  })}`
                                : `Created at: ${new Date(
                                    item.createdAt
                                  ).toLocaleDateString([], {
                                    month: "short",
                                    day: "2-digit",
                                    year: "numeric",
                                  })}, ${new Date(
                                    item.createdAt
                                  ).toLocaleTimeString([], {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    hour12: true,
                                  })}`}
                            </p>
                          </div>
                        </li>
                      )
                    )
                  )}
                </ul>
              </Dropdown>
            </div>
            <div className="relative">
              <div
                onClick={() => setProfileDrop(!profileDrop)}
                className="flex items-center gap-2 cursor-pointer"
              >
                <div
                  className={`rounded-full overflow-hidden sm:p-[3px] p-[1px] border sm:w-10 sm:h-10 w-7 h-7 ${
                    profileImage && "border-primary/50"
                  }`}
                >
                  {profileImage ? (
                    <Image
                      src={profileImage?.toString()}
                      alt="logo"
                      width={300}
                      height={300}
                      className="w-full h-full object-cover rounded-full"
                    />
                  ) : (
                    <div className="w-full h-full rounded-full bg-yellow-100 flex items-center justify-center">
                      <User size={20} className="text-yellow-600" />
                    </div>
                  )}
                </div>
                <CaretDown size={16} className=" text-primary" />
              </div>
              <Dropdown
                className={`w-[233px] py-4.5 px-15px`}
                isOpen={profileDrop}
                setIsOpen={setProfileDrop}
              >
                <ul className="text-primary font-medium text-base/6">
                  <li className="hover:text-green-600 hover:translate-x-2 transition-all duration-300">
                    <Link href={`/setting`}>My Profile</Link>
                  </li>
                  <hr className="my-[13px] border-green-600/15" />
                  <li className="hover:text-green-600 hover:translate-x-2 transition-all duration-300">
                    <button
                      type="button"
                      onClick={() => setIsLogoutModal(!isLogoutModal)}
                    >
                      Log Out
                    </button>
                  </li>
                </ul>
              </Dropdown>
            </div>
          </div>
        </div>
      </header>
      <CommonModal
        title="Log Out ?"
        isClose={isLogoutModal}
        setIsClose={setIsLogoutModal}
      >
        <p className="text-gray-500 text-sm/5 pt-5 max-w-[465px]">
          Are you sure you want to log out?
        </p>
        <div className="flex  items-center justify-end pt-[34px]  gap-3.5">
          <Button
            variant="outlinedGreen"
            className="sm:min-w-[157px] w-full sm:w-auto"
            onClick={() => setIsLogoutModal(false)}
          >
            no
          </Button>
          <Button
            variant="filledGreen"
            className="sm:min-w-[157px] w-full sm:w-auto"
            onClick={handleLogout}
          >
            Yes, Log out
          </Button>
        </div>
      </CommonModal>
      <div
        className={`fixed top-0 left-0 z-[999] w-full bg-black/30  h-screen lg:hidden transition-all duration-300 ${
          isOpen ? "opacity-100 visible" : "opacity-0 invisible"
        }`}
      >
        <nav
          className={`p-5 max-w-[250px] w-full bg-white h-full transition-all duration-500 ${
            isOpen ? "translate-x-0" : "-translate-x-full"
          }`}
        >
          <div className="flex items-center justify-between">
            <Link href="/dashboard" className=" max-w-36">
              <Image
                src="/assets/images/newHome/Home-logo.png"
                alt="logo"
                width={300}
                height={300}
                className="w-full h-auto object-contain"
              />
            </Link>
            <X
              size={20}
              className="cursor-pointer text-primary"
              onClick={() => setIsOpen(false)}
            />
          </div>
          <ul className="flex flex-col gap-3 pt-7">
            <li
              className={`py-[18px] px-6 text-base/4 font-medium ${
                pathname === "/dashboard"
                  ? "text-yellow-600 bg-yellow-100"
                  : "text-gray-500"
              } flex items-center gap-3 rounded-full cursor-pointer`}
            >
              <Link href="/dashboard" className="flex items-center gap-3">
                <DashboardIcon
                  className="w-6 h-6"
                  pathFillColor={
                    pathname === "/dashboard" ? "#c58843" : "#5E585A"
                  }
                />
                Dashboard
              </Link>
            </li>
            <li
              className={`py-[18px] px-6 text-base/4 font-medium ${
                pathname === "/session"
                  ? "text-yellow-600 bg-yellow-100"
                  : "text-gray-500"
              } flex items-center gap-3 rounded-full cursor-pointer`}
            >
              <Link href="/session" className="flex items-center gap-3">
                <SessionIcon
                  className="w-6 h-6"
                  pathFillColor={
                    pathname === "/session" ? "#c58843" : "#5E585A"
                  }
                />
                Sessions
              </Link>
            </li>
            <li
              className={`py-[18px] px-6 text-base/4 font-medium ${
                pathname === "/payment"
                  ? "text-yellow-600 bg-yellow-100"
                  : "text-gray-500"
              } flex items-center gap-3 rounded-full cursor-pointer`}
            >
              <Link href="/payment" className="flex items-center gap-3">
                <PaymentIcon
                  className="w-6 h-6"
                  pathFillColor={
                    pathname === "/payment" ? "#c58843" : "#5E585A"
                  }
                />
                Payments
              </Link>
            </li>
            <li
              className={`py-[18px] px-6 text-base/4 font-medium ${
                pathname === "/patient"
                  ? "text-yellow-600 bg-yellow-100"
                  : "text-gray-500"
              } flex items-center gap-3 rounded-full cursor-pointer`}
            >
              <Link href="/patient" className="flex items-center gap-3">
                <PatientIcon
                  className="w-6 h-6"
                  pathFillColor={
                    pathname === "/patient" ? "#c58843" : "#5E585A"
                  }
                />
                Clients
              </Link>
            </li>
            <li
              className={`py-[18px] px-6 text-base/4 font-medium ${
                pathname === "/public-calendar"
                  ? "text-yellow-600 bg-yellow-100"
                  : "text-gray-500"
              } flex items-center gap-3 rounded-full cursor-pointer`}
            >
              <Link href="/public-calendar" className="flex items-center gap-3">
                <PublicCalendarIcon
                  className="w-6 h-6"
                  pathFillColor={
                    pathname === "/public-calendar" ? "#c58843" : "#5E585A"
                  }
                />
                Public Calendar
              </Link>
            </li>
          </ul>
        </nav>
      </div>
    </Fragment>
  );
};

export default Header;
