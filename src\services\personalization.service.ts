import axiosInstance from "@/utils/axios";
import endpoints from "@/utils/endpoints";

// Fetch personalization URL
export async function fetchPersonalizationUrl() {
  try {
    // This would be the actual endpoint for fetching the URL
    const response = await axiosInstance.get(endpoints.publicCalendar);
    return response.data;
  } catch (error) {
    console.error("Error fetching personalization URL:", error);
    throw error;
  }
}

