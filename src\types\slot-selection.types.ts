export type TherapistProfileData = {
  _id: string;
  identifier: string;
  name: string;
  fullName: string;
  pronouns: string;
  profileImage: string;
  profilePicUrl: string;
  experience: string;
  yearsOfExperience: number;
  location: string;
  designation: string;
  minFee: number;
  maxFee: number;
  languages?: string[];
};

export type SessionMode = {
  name: string;
  duration: string;
  selected: boolean;
  slotType?: string;
};

export interface VerificationDetails {
  docs?: string[];
  featuresNeed?: string[];
  genderPreference?: string[];
  practicingTitle?: string;
  sentForVerification?: boolean;
  source?: string[];
  uploadedDocsCount?: number;
  yearsOfExperience?: string;
}
