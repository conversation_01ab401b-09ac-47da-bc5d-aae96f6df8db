# Client Landing Page Implementation

## Overview

This directory contains the complete client-facing landing page for the Thought Pudding therapy platform, featuring a modern, responsive design with interactive components, therapist profiles, and comprehensive service information.

## Key Features Implemented

### Landing Page Sections
- **Hero Section**: Eye-catching introduction with call-to-action
- **Question Bubbles Section**: Interactive FAQ-style component with animated SVG backgrounds
- **How It Works Section**: Step-by-step therapy process explanation
- **Why Different Section**: Platform differentiators and value propositions
- **Services Section**: Configurable therapy service cards with custom illustrations
- **Who We Are Section**: Interactive therapist carousel with profile cards
- **Footer**: Comprehensive site navigation and contact information

### Therapist Management System
- **Dynamic Therapist Profiles**: Individual therapist detail pages
- **Carousel Display**: Interactive therapist showcase with navigation
- **Profile Generation**: Automatic URL generation and routing
- **Data Management**: Centralized therapist data with TypeScript interfaces

### Design System
- **Configurable Components**: Reusable therapy cards with customizable themes
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Typography**: Caveat font integration for brand consistency
- **Color Schemes**: Purple and pink themed components with beige accents

## Directory Structure

```
src/app/clients/Landing Page/
├── components/
│   ├── HeroSection.tsx                 # Main landing hero
│   ├── QuestionBubblesSection.tsx      # Interactive FAQ section
│   ├── HowItWorksSection.tsx           # Process explanation
│   ├── WhyDifferentSection.tsx         # Value propositions
│   ├── ServicesSection.tsx             # Therapy service cards
│   ├── WhoWeAreSection.tsx             # Therapist carousel
│   └── Footer.tsx                      # Site footer
├── data/
│   └── therapists.json                 # Therapist profile data
├── utils/
│   └── therapists.ts                   # Therapist utility functions
└── types/
    └── therapist.ts                    # TypeScript interfaces

src/app/therapists/[slug]/
├── page.tsx                            # Dynamic therapist profile page
└── not-found.tsx                       # 404 error handling

src/components/client/
├── BeginTherapyButton.tsx              # CTA button with email integration
├── TherapyCard.tsx                     # Configurable service cards
└── TherapistDetailPage.tsx             # Individual therapist profiles
```

## Technical Implementation

### Therapist Profile System

#### Data Structure
```typescript
interface TherapistData {
  id: string;
  name: string;
  pronouns?: string;
  title: string;
  experience: string;
  sessionFormat: string;
  image: string;
  therapyDescription: string;
  languages: string[];
  values: string[];
  therapyTypes: string[];
  concerns: string[];
  qualifications: string[];
  fees: {
    currency: string;
    amount: number | string;
    description?: string;
  };
  accentColor: 'pink' | 'purple';
  rotation?: number;
}
```

#### URL Generation System
The platform automatically generates SEO-friendly URLs for therapist profiles:

```typescript
// Example: "Anusha Arora" → "/therapists/anusha-arora"
function generateTherapistUrl(therapist: TherapistData): string {
  const slug = therapist.name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .replace(/^-+|-+$/g, '');
  return `/therapists/${slug}`;
}
```

#### Dynamic Routing
- **Route**: `/therapists/[slug]`
- **Page Component**: Renders individual therapist profiles
- **Error Handling**: Custom 404 page for invalid therapist URLs
- **Navigation**: Automatic back-linking to main landing page sections

### Configurable Service Cards

The `TherapyCard` component supports extensive customization:

```typescript
interface TherapyCardProps {
  title: string;                    // Main section title
  cardTitle: string;                // Inner card heading
  cardDescription: string;          // Service description
  tagText: string;                  // Bottom tag text
  svgPath: string;                  // Illustration path
  svgSize?: { width: number; height: number };
  svgPosition?: { top: string; left: string };
  innerCardSize?: { width: number; height: number };
  outerBgColor?: string;            // Outer background color
  innerBgColor?: string;            // Inner card color
}
```

#### Current Service Cards
1. **Individual & Couples Therapy** (Purple theme)
2. **Mental Health Assessments** (Pink theme)

### Email Integration System

Both "Begin Therapy" and "Book Session" buttons integrate with email:

```typescript
const emailUrl = "mailto:<EMAIL>?subject=Ready%20to%20take%20the%20first%20step%2C%20looking%20for%20therapy&body=Hi%20Thought%20Pudding%20Team%2C%0D%0AMy%20name%20is%20%5BYour%20Name%5D%2C%20and%20I%E2%80%99m%20ready%20to%20take%20the%20first%20step%20towards%20therapy";
```

## Design Features

### Interactive Therapist Carousel
- **Auto-advance**: 5-second intervals
- **Manual Navigation**: Previous/next buttons and dot indicators  
- **Card Overlapping**: Layered display with hover effects
- **Responsive Layout**: Adapts to different screen sizes
- **Centered Content**: All therapist information perfectly aligned

### Question Bubbles Animation
- **SVG Background**: Custom animated illustration
- **Interactive Elements**: Expandable question sections
- **Smooth Transitions**: CSS animations for user interactions

### Responsive Typography
- **Caveat Font**: Brand-consistent headings and titles
- **Roboto Font**: Clean, readable body text
- **Scalable Sizes**: Responsive text sizing across devices

## Navigation Flow

### User Journey
1. **Landing Page** (`/clients`) - Main entry point
2. **Service Exploration** - Therapy cards and information sections  
3. **Therapist Discovery** - Carousel browsing in "Who We Are"
4. **Profile Deep Dive** - Individual therapist pages (`/therapists/[slug]`)
5. **Contact Initiation** - Email integration via CTA buttons

### Internal Linking
- **Header Navigation**: About Us → Who We Are section
- **Services Navigation**: Direct section linking
- **Therapist Links**: Auto-generated profile URLs
- **Error Handling**: 404 pages redirect to relevant sections

## Responsive Design

### Breakpoints
- **Mobile**: `px-4 py-8` spacing
- **Tablet**: `md:px-[75px] md:py-[60px]` spacing  
- **Desktop**: `lg:px-[150px] lg:py-[100px]` spacing

### Layout Adaptations
- **Carousel**: Single card on mobile, multiple on desktop
- **Service Cards**: Stacked on mobile, side-by-side on desktop
- **Navigation**: Hamburger menu on mobile, full nav on desktop

## Development Notes

### Component Architecture
- **Functional Components**: All components use React function syntax
- **TypeScript**: Full type safety with interfaces
- **Modular Design**: Reusable components with prop-based customization
- **Clean Code**: Atomic commits and descriptive variable names

### Performance Optimizations
- **Image Optimization**: WebP format and lazy loading
- **Code Splitting**: Dynamic imports for non-critical components
- **Minimal Client-Side**: Prefer server components where possible

### Error Handling
- **Graceful Degradation**: Fallback content for missing data
- **User-Friendly Errors**: Custom 404 pages with navigation options
- **Type Safety**: Comprehensive TypeScript interfaces

---

## Development Setup

### Adding New Therapists
1. Add therapist data to `data/therapists.json`
2. Add therapist image to `/public/assets/images/therapists/`
3. The system automatically generates URLs and carousel entries

### Adding New Service Cards
1. Create SVG illustration in `/public/assets/images/client/`
2. Add `TherapyCard` component to `ServicesSection.tsx`
3. Configure colors, sizing, and content via props

### Modifying Sections
Each section is a standalone component that can be:
- Reordered in the main page layout
- Customized via props and styling
- Extended with additional features
