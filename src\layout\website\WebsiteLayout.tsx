"use client";
import Footer from "@/app/Landing Page/Footer";
import SplashScreen from "@/components/common/SplashScreen";
import { useAuth } from "@/context/AuthContext";
import { usePathname, useRouter } from "next/navigation";
import React, { lazy, Suspense, useEffect, useState } from "react";

const Header = lazy(() => import("./Header"));

const WebsiteLayout: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const router = useRouter();
  const pathname = usePathname(); // Get the current path
  const { isAuthenticated, loading, isRedirecting, setIsRedirecting } = useAuth() || {
    isAuthenticated: false,
    loading: true,
    isRedirecting: false,
    setIsRedirecting: () => {}
  };
  const [showLoader, setShowLoader] = useState(false);

  useEffect(() => {
    if (isAuthenticated && (pathname === "/therapist" || pathname === "/")) {
      setIsRedirecting(true);
      setShowLoader(true);

      // Show loader for 2 seconds before redirecting
      setTimeout(() => {
        router.push("/dashboard");
        setShowLoader(false);
        setIsRedirecting(false);
      }, 2000);
    }
  }, [isAuthenticated, pathname, router, setIsRedirecting]);

  // Show loading screen while checking authentication
  if (loading) {
    return <SplashScreen />;
  }

  // Show loading screen during successful login redirect
  if (showLoader || isRedirecting) {
    return <SplashScreen />;
  }

   const isIOS = () => {
    if (typeof window !== "undefined") {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
             (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    }
    return false;
  };
  
  const backgroundStyles = {
    backgroundImage: "url('/assets/images/newHome/bg-home.png')",
    backgroundSize: isIOS() ? "contain" : "cover",
    backgroundPosition: "center",
    backgroundAttachment: isIOS() ? "scroll" : "fixed",
  };
  return (
    <Suspense fallback={<SplashScreen />}>
      <div className="h-full">
        <Header />
        <div>
          <div>{children}</div>
        </div>
        <div className={`lg:px-[150px] lg:py-[40px] md:px-[75px] md:py-[20px] mx-4 my-2  ${isIOS() ? "" : "bg-cover bg-center bg-no-repeat"}`}  style={backgroundStyles}>
          <Footer />
        </div>
      </div>
    </Suspense>
  );
};

export default WebsiteLayout;
