"use client";
import React from 'react';

interface AnimatedButtonProps {
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  isLoading?: boolean;
  isSuccess?: boolean;
  disabled?: boolean;
  className?: string;
  loadingText?: string;
  successText?: string;
  children: React.ReactNode;
}

const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  onClick,
  isLoading = false,
  isSuccess = false,
  disabled = false,
  className = "",
  loadingText = "Loading...",
  successText = "Success!",
  children
}) => {
  // Determine button state and styling
  const getButtonState = () => {
    if (isSuccess) {
      return {
        bgColor: 'bg-green-600 hover:bg-green-700',
        content: (
          <>
            <svg className="w-5 h-5 mr-2 animate-scale-in" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth="2" 
                d="M5 13l4 4L19 7"
                className="animate-draw-check"
              ></path>
            </svg>
            {successText}
          </>
        )
      };
    }
    
    if (isLoading) {
      return {
        bgColor: 'bg-blue-600 hover:bg-blue-700',
        content: (
          <>
            <div className="relative mr-2">
              <div className="w-5 h-5">
                <div className="absolute inset-0 opacity-25 rounded-full border-2 border-white"></div>
                <div className="absolute inset-0 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            </div>
            {loadingText}
          </>
        )
      };
    }
    
    return {
      bgColor: 'bg-blue-600 hover:bg-blue-700',
      content: children
    };
  };
  
  const { bgColor, content } = getButtonState();
  
  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled || isLoading || isSuccess}
      className={`relative overflow-hidden px-6 py-3 rounded-full text-white font-medium transition-all duration-300 
        ${bgColor} disabled:opacity-80 disabled:cursor-not-allowed flex items-center justify-center
        ${isLoading || isSuccess ? 'shadow-lg' : 'shadow hover:shadow-lg'}
        ${className}`}
    >
      {/* Button ripple effect on hover */}
      <span className="absolute inset-0 w-full h-full bg-white/10 scale-0 rounded-full opacity-0 hover:scale-100 hover:opacity-100 transition-all duration-500"></span>
      
      {/* Button content */}
      <span className="relative z-10 flex items-center justify-center">
        {content}
      </span>
    </button>
  );
};

export default AnimatedButton;
