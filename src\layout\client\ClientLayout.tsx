"use client";
import { ReactNode, Suspense } from "react";
import ClientHeader from "./ClientHeader";

interface ClientLayoutProps {
  children: ReactNode;
}

// Loading component for header
function HeaderFallback() {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex-shrink-0">
            <div className="h-8 w-32 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="hidden md:flex space-x-8">
            <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      </div>
    </header>
  );
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <div className="min-h-screen">
      <Suspense fallback={<HeaderFallback />}>
        <ClientHeader />
      </Suspense>
      <main>{children}</main>
    </div>
  );
}