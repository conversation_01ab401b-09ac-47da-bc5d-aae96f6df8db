import useSWR from 'swr';
import axiosInstance from '@/utils/axios';
import endpoints from '@/utils/endpoints';

interface ApiResponse<T> {
  responseCode: number;
  status: string;
  message: string;
  data: T;
}

interface TotalWorkingHoursData {
  therapistId: string;
  workingHours: string;
  sessionCount: number;
}

export interface StrongRelationshipsData {
  totalClients: number;
  powerClientsCount: number;
  powerClientsData: Array<{
    totalRevenue: number;
    clientId: string;
    name: string;
    email: string;
  }>;
}

export interface PeopleHelpingData {
  activeClients: Array<{
    _id: string;
    sessionCount: number;
    email: string;
    name: string;
    isActive: boolean;
  }>;
  totalActiveClients: number;
  minSessionsThreshold: number;
  analysisRange: string;
  generatedAt: string;
}

export interface RuptureRepairData {
  totalRiskClients: number;
  riskClients: Array<{
    totalSessions: number;
    cancelledSessions: number;
    rescheduledSessions: number;
    totalPayments: number;
    delayedPayments: number;
    cancelledPayments: number;
    isRescheduleRisk: boolean;
    isCancellationRisk: boolean;
    isDelayedPaymentRisk: boolean;
    isCancelledPaymentRisk: boolean;
    clientId: string;
    name: string;
    email: string;
    phone: string;
    isActive: boolean;
    reschedulePercentage: number;
    cancellationPercentage: number;
    delayedPaymentPercentage: number | null;
    cancelledPaymentPercentage: number | null;
    maxPercentage: number;
  }>;
  riskCategoryCounts: {
    'Reschedule Risk (>50%)': number;
    'Cancellation Risk (>50%)': number;
    'Delayed Payment Risk (>50%)': number;
    'Cancelled Payment Risk (>50%)': number;
  };
  criteria: string;
  analysisRange: string;
  generatedAt: string;
}

export interface BusiestTimeSlotsData {
  busiestTimeSlots: Array<{
    sessionCount: number;
    slotStartHour: number;
    rank: number;
    timeSlot: string;
  }>;
  totalTimeSlots: number;
  analysisRange: string;
  generatedAt: string;
}

export interface BusiestDaysData {
  busiestDays: Array<{
    dayOfWeek: string;
    dayNumber: number;
    totalSessions: number;
    percentage: number;
  }>;
  totalSessions: number;
  analysisRange: string;
  generatedAt: string;
}

export interface HighestCancellationDayData {
  highestCancellationDays: Array<{
    dayOfWeek: string;
    dayNumber: number;
    totalCancellations: number;
    percentage: number;
  }>;
  totalCancellations: number;
  analysisRange: string;
  generatedAt: string;
}

export interface PaymentSummaryData {
  paymentSummary: {
    receivedAfterSessions: {
      amount: number;
      count: number;
      description: string;
    };
    cancellationFees: {
      amount: number;
      count: number;
      description: string;
    };
    advancePayments: {
      amount: number;
      count: number;
      description: string;
    };
    summary: {
      totalAmount: number;
      totalTransactions: number;
    };
  };
  analysisRange: string;
  generatedAt: string;
}

export const useGetTotalWorkingHours = (fromDate: string, toDate: string) => {
  const { data, error, isLoading } = useSWR(
    // Only create the key if both dates are provided
    fromDate && toDate ? [endpoints.analytics.totalWorkingHours, fromDate, toDate] : null,
    () =>
      axiosInstance.get<ApiResponse<TotalWorkingHoursData>>(endpoints.analytics.totalWorkingHours, {
        params: { fromDate, toDate },
      })
  );

  return {
    totalWorkingHoursData: data?.data?.data,
    totalWorkingHoursError: error,
    totalWorkingHoursLoading: isLoading,
  };
};

export const useGetStrongRelationships = (fromDate: string, toDate: string) => {
  const { data, error, isLoading } = useSWR(
    // Only create the key if both dates are provided
    fromDate && toDate ? [endpoints.analytics.strongRelationships, fromDate, toDate] : null,
    () =>
      axiosInstance.get<ApiResponse<StrongRelationshipsData>>(endpoints.analytics.strongRelationships, {
        params: { fromDate, toDate },
      })
  );

  return {
    strongRelationshipsData: data?.data?.data,
    strongRelationshipsError: error,
    strongRelationshipsLoading: isLoading,
  };
};

export const useGetPeopleHelping = (fromDate: string, toDate: string) => {
  const { data, error, isLoading } = useSWR(
    // Only create the key if both dates are provided
    fromDate && toDate ? [endpoints.analytics.peopleHelping, fromDate, toDate] : null,
    () =>
      axiosInstance.get<ApiResponse<PeopleHelpingData>>(endpoints.analytics.peopleHelping, {
        params: { fromDate, toDate },
      })
  );

  return {
    peopleHelpingData: data?.data?.data,
    peopleHelpingError: error,
    peopleHelpingLoading: isLoading,
  };
};

export const useGetRuptureRepair = (fromDate: string, toDate: string) => {
  const { data, error, isLoading } = useSWR(
    // Only create the key if both dates are provided
    fromDate && toDate ? [endpoints.analytics.ruptureRepair, fromDate, toDate] : null,
    () =>
      axiosInstance.get<ApiResponse<RuptureRepairData>>(endpoints.analytics.ruptureRepair, {
        params: { fromDate, toDate },
      })
  );

  return {
    ruptureRepairData: data?.data?.data,
    ruptureRepairError: error,
    ruptureRepairLoading: isLoading,
  };
};

export const useGetBusiestTimeSlots = (fromDate: string, toDate: string) => {
  const { data, error, isLoading } = useSWR(
    fromDate && toDate ? [endpoints.analytics.busiestTimeSlots, fromDate, toDate] : null,
    () =>
      axiosInstance.get<ApiResponse<BusiestTimeSlotsData>>(endpoints.analytics.busiestTimeSlots, {
        params: { fromDate, toDate },
      })
  );

  return {
    busiestTimeSlotsData: data?.data?.data,
    busiestTimeSlotsError: error,
    busiestTimeSlotsLoading: isLoading,
  };
};

export const useGetBusiestDays = (fromDate: string, toDate: string) => {
  const { data, error, isLoading } = useSWR(
    fromDate && toDate ? [endpoints.analytics.busiestDays, fromDate, toDate] : null,
    () =>
      axiosInstance.get<ApiResponse<BusiestDaysData>>(endpoints.analytics.busiestDays, {
        params: { fromDate, toDate },
      })
  );

  return {
    busiestDaysData: data?.data?.data,
    busiestDaysError: error,
    busiestDaysLoading: isLoading,
  };
};

export const useGetHighestCancellationDay = (fromDate: string, toDate: string) => {
  const { data, error, isLoading } = useSWR(
    fromDate && toDate ? [endpoints.analytics.highestCancellationDay, fromDate, toDate] : null,
    () =>
      axiosInstance.get<ApiResponse<HighestCancellationDayData>>(endpoints.analytics.highestCancellationDay, {
        params: { fromDate, toDate },
      })
  );

  return {
    highestCancellationDayData: data?.data?.data,
    highestCancellationDayError: error,
    highestCancellationDayLoading: isLoading,
  };
};

export const useGetPaymentSummary = (fromDate: string, toDate: string) => {
  const { data, error, isLoading } = useSWR(
    fromDate && toDate ? [endpoints.analytics.paymentSummary, fromDate, toDate] : null,
    () =>
      axiosInstance.get<ApiResponse<PaymentSummaryData>>(endpoints.analytics.paymentSummary, {
        params: { fromDate, toDate },
      })
  );

  return {
    paymentSummaryData: data?.data?.data,
    paymentSummaryError: error,
    paymentSummaryLoading: isLoading,
  };
};