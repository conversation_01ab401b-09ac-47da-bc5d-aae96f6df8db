// Utility functions for managing completed tabs in localStorage

const STORAGE_KEY = 'publicCalendar_completedTabs';

/**
 * Get the array of completed tab names from localStorage
 */
export const getCompletedTabs = (): string[] => {
  if (typeof window === 'undefined') {
    return [];
  }
  
  const storedTabs = localStorage.getItem(STORAGE_KEY);
  return storedTabs ? JSON.parse(storedTabs) : [];
};

/**
 * Mark a tab as completed and store in localStorage
 * @param tabName The name of the tab to mark as completed
 */
export const markTabAsCompleted = (tabName: string): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  const completedTabs = getCompletedTabs();
  
  // Only add the tab if it's not already in the array
  if (!completedTabs.includes(tabName)) {
    completedTabs.push(tabName);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(completedTabs));
  }
};

/**
 * Check if a tab has been completed
 * @param tabName The name of the tab to check
 * @returns True if the tab has been completed, false otherwise
 */
export const isTabCompleted = (tabName: string): boolean => {
  return getCompletedTabs().includes(tabName);
};

/**
 * Reset all completed tabs
 */
export const resetCompletedTabs = (): void => {
  if (typeof window === 'undefined') {
    return;
  }
  
  localStorage.removeItem(STORAGE_KEY);
};
