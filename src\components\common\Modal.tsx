import React, { useEffect, useState } from "react";
import ReactDOM from "react-dom";
import { IoCloseCircleOutline } from "react-icons/io5";

interface ModalProps {
  open: boolean;
  handler: () => void;
  backdropClose?: boolean;
  children: React.ReactNode;
  containerProps?: string;
  size?: string;
  email?: string;
}

const Modal: React.FC<ModalProps> = ({
  open,
  handler,
  backdropClose,
  children,
  containerProps = "",
  size = "sm",
}) => {
  const [container, setContainer] = useState<HTMLDivElement | null>(null);

  // Additional cleanup effect to ensure scroll is always restored
  useEffect(() => {
    return () => {
      // Force restore scroll when component unmounts
      if (typeof document !== "undefined") {
        const scrollbarElement = document.body;
        const htmlElement = document.documentElement;

        scrollbarElement.style.overflow = "";
        scrollbarElement.style.position = "";
        scrollbarElement.style.width = "";
        scrollbarElement.style.height = "";
        scrollbarElement.style.top = "";
        scrollbarElement.style.left = "";
        htmlElement.style.overflow = "";
      }
    };
  }, []);

  useEffect(() => {
    if (typeof document !== "undefined") {
      // Ensure that the container exists in the DOM
      const portalContainer = document.getElementById("__next");
      if (!portalContainer) {
        const newContainer = document.createElement("div");
        newContainer.id = "portal-root";
        document.body.appendChild(newContainer);
        setContainer(newContainer);
      } else {
        setContainer(portalContainer as HTMLDivElement);
      }
    }
  }, []);
  useEffect(() => {
    if (typeof document !== "undefined") {
      const scrollbarElement = document.body;
      const htmlElement = document.documentElement;

      if (open) {
        // Hide scrollbar when modal is open
        scrollbarElement.style.overflow = "hidden";
        htmlElement.style.overflow = "hidden";

        // Prevent iOS rubber band scrolling
        scrollbarElement.style.position = "fixed";
        scrollbarElement.style.width = "100%";
        scrollbarElement.style.height = "100%";
        scrollbarElement.style.top = "0";
        scrollbarElement.style.left = "0";
      } else {
        // Always restore to normal scroll state when modal is closed
        scrollbarElement.style.overflow = "";
        scrollbarElement.style.position = "";
        scrollbarElement.style.width = "";
        scrollbarElement.style.height = "";
        scrollbarElement.style.top = "";
        scrollbarElement.style.left = "";
        htmlElement.style.overflow = "";
      }

      // Close modal on 'Esc' key press
      const handleEscKeyPress = (event: KeyboardEvent) => {
        if (event.key === "Escape" && open) {
          handler(); // Close modal
        }
      };

      document.addEventListener("keydown", handleEscKeyPress);

      // Cleanup function
      return () => {
        document.removeEventListener("keydown", handleEscKeyPress);

        // Always restore scroll when component unmounts or modal closes
        scrollbarElement.style.overflow = "";
        scrollbarElement.style.position = "";
        scrollbarElement.style.width = "";
        scrollbarElement.style.height = "";
        scrollbarElement.style.top = "";
        scrollbarElement.style.left = "";
        htmlElement.style.overflow = "";
      };
    }
  }, [open, handler]);

  if (!container) {
    return null; // or a fallback loading state while waiting for the container
  }

  return ReactDOM.createPortal(
    <div
      className={`fixed inset-0 z-[9999] flex items-center justify-center bg-gray-800 bg-opacity-50 transition-opacity duration-300 ${
        open ? "opacity-100" : "opacity-0 pointer-events-none"
      }`}
      onClick={() => {
        if (!backdropClose) {
          handler();
        }
      }}
    >
      <div
        className={`bg-white rounded-md shadow-lg transform transition-transform duration-300 overflow-y-auto ${containerProps} ${
          open ? "scale-100" : "scale-90"
        }
        ${
          size === "xxs"
            ? "w-[95%] sm:w-[90%] md:w-2/5 lg:w-2/6 2xl:w-1/5 max-w-[95%] sm:max-w-[90%] md:max-w-[40%] lg:max-w-[33.333333%] 2xl:max-w-[20%] max-h-[90vh] sm:max-h-[85vh] md:max-h-[80vh] mx-2 sm:mx-4 my-6 sm:my-8 md:my-12 p-3 sm:p-4"
            : size === "xs"
            ? "w-[95%] sm:w-[90%] md:w-3/5 lg:w-2/5 2xl:w-1/4 max-w-[95%] sm:max-w-[90%] md:max-w-[60%] lg:max-w-[40%] 2xl:max-w-[25%] max-h-[90vh] sm:max-h-[85vh] md:max-h-[80vh] mx-2 sm:mx-4 my-6 sm:my-8 md:my-12 p-3 sm:p-4"
            : size === "sm"
            ? "w-[95%] sm:w-[90%] md:w-2/3 lg:w-2/4 2xl:w-1/3 max-w-[95%] sm:max-w-[90%] md:max-w-[66.666667%] lg:max-w-[50%] 2xl:max-w-[33.333333%] max-h-[90vh] sm:max-h-[85vh] md:max-h-[80vh] mx-2 sm:mx-4 my-6 sm:my-8 md:my-12 p-3 sm:p-4"
            : size === "md"
            ? "w-[95%] sm:w-[90%] md:w-3/4 lg:w-3/5 2xl:w-2/5 max-w-[95%] sm:max-w-[90%] md:max-w-[75%] lg:max-w-[60%] 2xl:max-w-[40%] max-h-[90vh] sm:max-h-[85vh] md:max-h-[80vh] mx-2 sm:mx-4 my-6 sm:my-8 md:my-12 p-3 sm:p-4"
            : size === "lg"
            ? "w-[95%] sm:w-[90%] md:w-5/6 lg:w-3/4 2xl:w-3/5 max-w-[95%] sm:max-w-[90%] md:max-w-[83.333333%] lg:max-w-[75%] 2xl:max-w-[60%] max-h-[90vh] sm:max-h-[85vh] md:max-h-[80vh] mx-2 sm:mx-4 my-6 sm:my-8 md:my-12 p-3 sm:p-4"
            : size === "xl"
            ? "w-[96%] sm:w-[92%] md:w-5/6 2xl:w-3/4 max-w-[96%] sm:max-w-[92%] md:max-w-[83.333333%] 2xl:max-w-[75%] max-h-[88vh] sm:max-h-[85vh] md:max-h-[82vh] mx-2 sm:mx-4 md:mx-6 my-6 sm:my-8 md:my-12 p-2 sm:p-3 md:p-4"
            : size === "xxl"
            ? "flex flex-col w-screen min-w-[100vw] max-w-[100vw] h-screen min-h-[100vh] max-h-[100vh] m-0 rounded-none p-4"
            : ""
        }
        `}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          className="absolute top-2 right-2 sm:top-3 sm:right-3 text-gray-500 hover:text-gray-700 text-lg sm:text-xl font-bold z-10"
          onClick={handler}
          aria-label="Close modal"
        >
          <IoCloseCircleOutline className="w-6 h-6 sm:w-8 sm:h-8" />
        </button>

        <div className="pt-6 sm:pt-8 pb-2 sm:pb-4 px-1 sm:px-2 md:px-4">
          {children}
        </div>
      </div>
    </div>,
    container
  );
};

export default Modal;
