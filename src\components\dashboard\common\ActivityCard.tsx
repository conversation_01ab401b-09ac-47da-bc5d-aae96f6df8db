import { ArrowDownLeft, ArrowUpRight } from "@phosphor-icons/react";
import React from "react";

const ActivityCard: React.FC<{
  title: string;
  count: number | string;
  percentage: string;
  borderColor: string;
}> = ({ title, count, percentage, borderColor }) => {
  const isNegative = percentage.startsWith("-");
  return (
    <div
      className={`bg-green-600/5 sm:px-5 sm:py-[25px] p-3 rounded-base border-l ${borderColor} `}
    >
      <p className="text-lg/7 text-primary">{title}</p>
      <div className="flex items-center gap-4 sm:pt-3 pt-2">
        <p className="sm:text-2xl/9 text-xl text-primary font-medium">
          {count}
        </p>
        {percentage && (
          <p
            className={`text-sm/4 flex items-center gap-1 ${
              isNegative ? "text-[#FF2727]" : "text-green-400"
            } `}
          >
            {isNegative ? (
              <ArrowDownLeft size={16} />
            ) : (
              <ArrowUpRight size={16} />
            )}{" "}
            {Number(percentage)?.toFixed(2)}%
          </p>
        )}
      </div>
    </div>
  );
};

export default ActivityCard;
