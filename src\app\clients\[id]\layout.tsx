import { Metadata } from 'next';
import { getPublicTherapistById, TherapistData } from '@/services/public-calendar.service';
import { getSocialMediaImageUrl } from '@/utils/imageUtils';

type Props = {
  params: { id: string };
  children: React.ReactNode;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = params;

  try {
    // Fetch therapist data for meta tags
    const response = await getPublicTherapistById(id);
    const therapistData = response as unknown as TherapistData;

    // Helper function to safely extract string values
    const safeString = (value: string | Record<string, unknown> | null | undefined): string => {
      if (typeof value === 'string') return value;
      return '';
    };



    const therapistName = safeString(therapistData?.name) || 'Therapist';

    // Use practicingTitle from verificationDetails as the primary designation
    const verificationDetails = therapistData?.verificationDetails as { practicingTitle?: string } | undefined;
    const therapistDesignation = verificationDetails?.practicingTitle || safeString(therapistData?.designation) || 'Licensed Therapist';



    // Handle profile image with compression for social media link previews
    // Get the current request URL to determine the correct domain for compression endpoint
    const compressionBaseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://dev.thoughtpudding.com';
    const profileImage = getSocialMediaImageUrl(
      therapistData?.profilePicUrl,
      '', // No fallback image, will be handled by console logging
      compressionBaseUrl // Use the correct domain for compression endpoint
    );

    const therapyApproach = safeString(therapistData?.practiceApproach) || 'Professional therapy services';

    // Create the page URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://app.thoughtpudding.com';
    const pageUrl = `${baseUrl}/clients/${id}`;

    // Create the title and description according to the new template
    const title = `Book therapy with ${therapistName} - ${therapistDesignation}`;

    // Use the practiceApproach (How therapy works with me) as the main description
    // Limit to a reasonable length for social media previews
    const description = therapyApproach.length > 160
      ? `${therapyApproach.substring(0, 157)}...`
      : therapyApproach;

    // Ensure profile image has full URL
    // const fullProfileImageUrl = profileImage.startsWith('http')
    //   ? profileImage
    //   : profileImage.startsWith('/')
    //     ? `${baseUrl}${profileImage}`
    //     : `${baseUrl}/${profileImage}`;



    return {
      title,
      description,
      keywords: [
        'therapy',
        'counseling',
        'mental health',
        'therapist',
        therapistName,
        therapistDesignation,
        'online therapy',
        'book session',
        'thought pudding'
      ],
      authors: [{ name: 'Thought Pudding' }],
      creator: 'Thought Pudding',
      publisher: 'Thought Pudding',

      // Open Graph tags for social media sharing - simplified structure like Airbnb
      openGraph: {
        title,
        description,
        url: pageUrl,
        siteName: 'Thought Pudding',
        type: 'website', // Changed from 'profile' to 'website' for better compatibility
        locale: 'en_US',
        images: [
          {
            url: profileImage,
            secureUrl: profileImage,
            width: 1200,
            height: 630,
            alt: `Book therapy with ${therapistName} - ${therapistDesignation}`,
            type: 'image/jpeg',
          }
        ],
      },

      // Twitter Card tags
      twitter: {
        card: 'summary_large_image',
        site: '@thoughtpudding',
        creator: '@thoughtpudding',
        title,
        description,
        images: [profileImage], // Use the same image as Open Graph
      },

      // Additional meta tags - simplified like Airbnb
      other: {
        // Basic Open Graph tags (matching Airbnb structure)
        'og:url': pageUrl,
        'og:type': 'website',
        'og:title': title,
        'og:description': description,
        'og:image': profileImage,

        // Twitter Card tags (matching Airbnb structure)
        'twitter:card': 'summary_large_image',
        'twitter:domain': 'thoughtpudding.com',
        'twitter:url': pageUrl,
        'twitter:title': title,
        'twitter:description': description,
        'twitter:image': profileImage,

        // App specific tags
        'theme-color': '#6D84FF',
        'msapplication-TileColor': '#6D84FF',
        'application-name': 'Thought Pudding',
        'apple-mobile-web-app-title': 'Thought Pudding',
        'apple-mobile-web-app-capable': 'yes',
        'apple-mobile-web-app-status-bar-style': 'default',
        'format-detection': 'telephone=no',
      },

      // Robots and indexing
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },

      // Verification tags (if needed)
      verification: {
        google: process.env.GOOGLE_VERIFICATION_ID,
      },
    };
  } catch (error) {
    console.error('Error generating metadata for therapist profile:', error);

    // Fallback metadata if therapist data fetch fails
    const fallbackBaseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://app.thoughtpudding.com';
    const fallbackProfileImage = ''; // No fallback image

    return {
      title: 'Book therapy with Professional Therapist - Licensed Therapist',
      description: 'Professional therapy services. Secure, confidential, and professional mental health sessions.',
      openGraph: {
        title: 'Book therapy with Professional Therapist - Licensed Therapist',
        description: 'Professional therapy services. Secure, confidential, and professional mental health sessions.',
        url: `${fallbackBaseUrl}/clients/${id}`,
        siteName: 'Thought Pudding',
        type: 'website',
        images: [
          {
            url: fallbackProfileImage,
            width: 1200,
            height: 630,
            alt: 'Thought Pudding - Professional Therapy Platform',
          }
        ],
      },
      twitter: {
        card: 'summary_large_image',
        site: '@thoughtpudding',
        title: 'Book therapy with Professional Therapist - Licensed Therapist',
        description: 'Professional therapy services. Secure, confidential, and professional mental health sessions.',
        images: [fallbackProfileImage],
      },
      other: {
        'og:url': `${fallbackBaseUrl}/clients/${id}`,
        'og:type': 'website',
        'og:title': 'Book therapy with Professional Therapist - Licensed Therapist',
        'og:description': 'Professional therapy services. Secure, confidential, and professional mental health sessions.',
        'og:image': fallbackProfileImage,
        'twitter:card': 'summary_large_image',
        'twitter:domain': 'thoughtpudding.com',
        'twitter:url': `${fallbackBaseUrl}/clients/${id}`,
        'twitter:title': 'Book therapy with Professional Therapist - Licensed Therapist',
        'twitter:description': 'Professional therapy services. Secure, confidential, and professional mental health sessions.',
        'twitter:image': fallbackProfileImage,
      },
    };
  }
}

export default function ClientLayout({ children }: Props) {
  return <>{children}</>;
}
