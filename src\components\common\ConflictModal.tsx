import React from "react";
import ReactD<PERSON> from "react-dom";
import But<PERSON> from "./Button";
import { IoCloseCircleOutline } from "react-icons/io5";
import { useEffect, useState } from "react";
import moment from "moment";
import { useRouter } from "next/navigation";

interface Conflict {
  startTime: string;
  endTime: string;
  clientName?: string;
  clientId?: string;
}

interface ConflictModalProps {
  open: boolean;
  onClose: () => void;
  message: string;
  conflicts: Conflict[];
  isCalendarSync?: boolean; // Flag to distinguish calendar sync from reschedule conflicts
}

const ConflictModal: React.FC<ConflictModalProps> = ({
  open,
  onClose,
  message,
  conflicts,
  isCalendarSync = false,
}) => {
  const router = useRouter();
  const [container, setContainer] = useState<HTMLDivElement | null>(null);

  useEffect(() => {
    if (typeof document !== "undefined") {
      // Ensure that the container exists in the DOM
      const portalContainer = document.getElementById("__next");
      if (!portalContainer) {
        const newContainer = document.createElement("div");
        newContainer.id = "portal-root";
        document.body.appendChild(newContainer);
        setContainer(newContainer);
      } else {
        setContainer(portalContainer as HTMLDivElement);
      }
    }
  }, []);

  useEffect(() => {
    if (typeof document !== "undefined" && open) {
      const scrollbarElement = document.body;

      // Hide scrollbar when modal is open
      if (scrollbarElement.style.overflow !== "hidden") {
        scrollbarElement.setAttribute("style", "overflow: hidden");
      }

      // Close modal on 'Esc' key press
      const handleEscKeyPress = (event: KeyboardEvent) => {
        if (event.key === "Escape" && open) {
          onClose(); // Close modal
        }
      };

      document.addEventListener("keydown", handleEscKeyPress);

      // Cleanup the event listener and restore the scrollbar
      return () => {
        document.removeEventListener("keydown", handleEscKeyPress);
      };
    }
  }, [open, onClose]);

  // Format date and time for display
  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = moment(dateTimeString);
      return {
        date: date.format("YYYY-MM-DD"),
        time: date.format("h:mm A"),
      };
    } catch {
      return {
        date: "Invalid date",
        time: "Invalid time",
      };
    }
  };

  // Handle redirect to session page with conflict date or client filter
  const handleRedirectToReschedule = () => {
    if (conflicts && conflicts.length > 0) {
      if (isCalendarSync && conflicts[0].clientName) {
        // For calendar sync conflicts, redirect with client name as search parameter
        // This will persist across pagination since search functionality works across all pages
        const clientName = encodeURIComponent(conflicts[0].clientName);
        router.push(`/session?searchClient=${clientName}`);
      } else {
        // For reschedule conflicts, redirect with conflict date
        const firstConflict = formatDateTime(conflicts[0].startTime);
        const conflictDate = firstConflict.date;
        router.push(`/session?conflictDate=${conflictDate}`);
      }
      onClose();
    }
  };

  if (!container || !open) {
    return null;
  }

  return ReactDOM.createPortal(
    <div
      className={`fixed inset-0 z-[99999] flex items-center justify-center bg-gray-800 bg-opacity-50 transition-opacity duration-300 ${
        open ? "opacity-100" : "opacity-0 pointer-events-none"
      }`}
      onClick={onClose}
    >
      <div
        className="bg-white rounded-md shadow-lg p-4 transform transition-transform duration-300 overflow-y-auto mx-4 my-6 sm:my-8 w-full md:w-3/4 lg:w-3/5 2xl:w-2/5 max-w-[90%] md:max-w-[75%] lg:max-w-[60%] 2xl:max-w-[40%] max-h-[90vh] sm:max-h-[85vh]"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-xl font-bold"
          onClick={onClose}
          aria-label="Close modal"
        >
          <IoCloseCircleOutline className="w-8 h-8 " />
        </button>

        <div className="pt-8 pb-4 px-2 sm:px-4">
          <div className="p-5">
            {isCalendarSync ? (
              <>
                <h2 className="text-xl font-semibold mb-4 text-red-600">Oops! You almost double-booked.</h2>
                <p className="text-base text-gray-700 mb-6">
                  {conflicts && conflicts.length > 0 ? (
                    conflicts[0].clientName ? (
                      <>
                        Seems like you already have <span className="font-semibold">{conflicts[0].clientName}</span>&apos;s session on{" "}
                        <span className="font-semibold">
                          {formatDateTime(conflicts[0].startTime).date} {formatDateTime(conflicts[0].startTime).time}
                        </span>
                        <br />
                        Want to sort it from here instead of juggling Google Calendar?
                      </>
                    ) : (
                      <>
                        Seems like you already have a session on{" "}
                        <span className="font-semibold">
                          {formatDateTime(conflicts[0].startTime).date} {formatDateTime(conflicts[0].startTime).time}
                        </span>
                        <br />
                        Want to sort it from here instead of juggling Google Calendar?
                      </>
                    )
                  ) : (
                    <>
                      Seems like you have conflicting sessions.
                      <br />
                      Want to sort it from here instead of juggling Google Calendar?
                    </>
                  )}
                </p>
              </>
            ) : (
              <>
                <h2 className="text-xl font-semibold mb-4 text-red-600">Sync Conflicts Found</h2>
                <p className="text-base text-gray-700 mb-6">{message}</p>
              </>
            )}

            <div className="flex justify-center">
              <Button
                onClick={handleRedirectToReschedule}
                className="px-6 py-2 rounded"
              >
                Yes
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>,
    container
  );
};

export default ConflictModal;