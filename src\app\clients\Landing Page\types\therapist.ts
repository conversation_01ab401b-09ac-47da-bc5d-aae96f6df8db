export interface TherapistData {
  // Basic identification
  id: string;
  slug: string; // URL-friendly identifier for routing
  
  // Personal information
  name: string;
  pronouns?: string; // e.g., "She/Her"
  title: string;
  image: string;
  accentColor: 'pink' | 'purple';
  
  // Professional details
  experience: string;
  sessionFormat: 'online' | 'in-person' | 'both';
  fees: {
    amount: number | string; // Can be a number or a range string like "2500-4000"
    currency: string;
    description?: string; // e.g., "Lorem ipsum lore"
  };
  
  // Qualifications
  qualifications: string[];
  
  // Languages
  languages: string[];
  
  // Therapy approach
  therapyDescription: string; // "What therapy looks like with me"
  
  // Specializations
  therapyTypes: string[];
  concerns: string[];
  values: string[];
  
  // Availability & booking
  isAvailable: boolean;
  bookingUrl?: string;
  
  // SEO & metadata
  metaTitle?: string;
  metaDescription?: string;
  
  // Display preferences
  rotation?: number; // For card tilt effect
  featured?: boolean; // For highlighting certain therapists
}

export interface TherapistFilters {
  therapyTypes?: string[];
  concerns?: string[];
  languages?: string[];
  sessionFormat?: 'online' | 'in-person' | 'both';
  priceRange?: {
    min: number;
    max: number;
  };
}

export interface TherapistSearchResult {
  therapists: TherapistData[];
  totalCount: number;
  filters: TherapistFilters;
} 