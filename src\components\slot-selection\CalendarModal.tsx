"use client";

interface CalendarModalProps {
  showCalendarModal: boolean;
  onClose: () => void;
  currentMonth: Date;
  onPrevMonth: () => void;
  onNextMonth: () => void;
  selectedDate: Date | null;
  datesWithSlots?: Set<string>; // Make optional since we're not using it
  formatDateForAPI?: (date: Date) => string; // Make optional since we're not using it
  onDateSelect: (date: Date) => void;
}

export default function CalendarModal({
  showCalendarModal,
  onClose,
  currentMonth,
  onPrevMonth,
  onNextMonth,
  selectedDate,
  onDateSelect,
}: CalendarModalProps) {
  if (!showCalendarModal) return null;

  // Calendar helper functions
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 m-4 max-w-sm w-full">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-[#251D5C]">
            Select Date
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>

        {/* Calendar Header */}
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={onPrevMonth}
            className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100"
          >
            <span className="text-gray-500">&lt;</span>
          </button>
          <h4 className="text-black font-medium gilmer-medium">
            {currentMonth.toLocaleString("default", {
              month: "long",
              year: "numeric",
            })}
          </h4>
          <button
            onClick={onNextMonth}
            className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100"
          >
            <span className="text-gray-500">&gt;</span>
          </button>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1">
          {/* Day headers */}
          {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
            (day, index) => (
              <div
                key={index}
                className="text-center text-xs font-medium text-gray-500 py-2 gilmer-medium"
              >
                {day}
              </div>
            )
          )}

          {/* Calendar days */}
          {(() => {
            const year = currentMonth.getFullYear();
            const month = currentMonth.getMonth();
            const daysInMonth = getDaysInMonth(year, month);
            const firstDay = getFirstDayOfMonth(year, month);
            const today = new Date();

            const days = [];

            // Add empty cells for days before the first day of the month
            for (let i = 0; i < firstDay; i++) {
              days.push(
                <div
                  key={`empty-${i}`}
                  className="h-10 w-10 mx-auto"
                ></div>
              );
            }

            // Add cells for each day of the month
            for (let day = 1; day <= daysInMonth; day++) {
              const date = new Date(year, month, day);
              const isToday =
                today.getDate() === day &&
                today.getMonth() === month &&
                today.getFullYear() === year;
              const isSelected =
                selectedDate &&
                selectedDate.getDate() === day &&
                selectedDate.getMonth() === month &&
                selectedDate.getFullYear() === year;
              const isPastDate = date < today && !isToday;

              days.push(
                <div
                  key={day}
                  className="text-center py-1"
                  onClick={() => !isPastDate && onDateSelect(date)}
                >
                  <div
                    className={`
                      h-10 w-10 mx-auto flex items-center justify-center text-sm font-medium
                      ${
                        isPastDate
                          ? "text-gray-300 cursor-not-allowed"
                          : isSelected
                          ? "bg-[#2C58BB] text-white rounded-full cursor-pointer"
                          : "text-gray-700 cursor-pointer hover:bg-gray-100"
                      }
                    `}
                  >
                    {day}
                  </div>
                </div>
              );
            }

            return days;
          })()}
        </div>
      </div>
    </div>
  );
}
