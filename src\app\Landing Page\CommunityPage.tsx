import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

interface Testimonial {
  name: string;
  image: string;
  title: string;
  nameBgImage: string;
  text: string;
}

const testimonials: Testimonial[] = [
  {
    name: "<PERSON><PERSON>",
    image: "/assets/images/newHome/community1.jpeg",
    title: "Trauma-informed Psychotherapist & Founder at Breakthrough Counselling ",
    nameBgImage: "assets/images/newHome/Name-bg2.png",
    text: "I have been using Thought Pudding for many months and have watched closely as the website has developed into the tool it is today. Thought Pudding integrates a plethora of several tools that helps therapists streamline their administrative tasks. *** Tracking payments is the task I dread the most, and Thought Pudding helps with just a few clicks! *** An invaluable tool for early career therapists.",
  },
  {
    name: "<PERSON>",
    image: "/assets/images/newHome/community2.jpeg",
    title: "Psychotherapist in Private Practice ",
    nameBgImage: "assets/images/newHome/Name-bg1.png",
    text: "As a private practitioner, switching between roles and apps tends to get frustrating. Thought Pudding has been a huge help in reducing my workload in this regard. I have been using the platform for more than 5 months now. *** You know it has been created keeping therapists in mind, offering everything I need to streamline my appointments without the administrative headache. My personal favourite feature is the automated reminders (via email) which have helped me and my clients avoid last-minute cancellations. *** It has also helped me track payments seamlessly.",
  },
  {
    name: "Jagadeesan S",
    image: "/assets/images/newHome/community3.jpeg",
    title: "Clinical Psychologist in Private Practice",
    nameBgImage: "assets/images/newHome/Name-bg2.png",
    text: "Thought Pudding has been so helpful for me in all the ways possible. *** It is affordable for therapists, simple to use, easy to schedule sessions to track and monitor the sessions provided for the clients. *** It also has features to calculate the payments I have received and are pending from my clients. It has made my work as a therapist so simple and easy. I would recommend it to all my friends and colleagues to benefit from Thought Pudding.",
  },
  {
    name: "Anushka",
    image: "/assets/images/newHome/community4.jpeg",
    title: "Queer-affirmative & Trauma informed Psychotherapist Founder at Real Haze Therapy",
    nameBgImage: "assets/images/newHome/Name-bg1.png",
    text: "Thought pudding is like an efficient personal assistant who predicts my needs, solves the problems and communicates the output to me! *** Using the platform has made my practice significantly easier and I no longer have to focus on administrative tasks. Recommend to any therapist looking to focus on therapy with clients *** and leave everything else to Thought Pudding. ",
  },
  {
    name: "Tanisha J ",
    image: "/assets/images/newHome/community5.jpeg",
    title: "Counselling Psychologist & Psychotherapist",
    nameBgImage: "assets/images/newHome/Name-bg2.png",
    text: "As a chronic organizer, I spend a lot of time on Excel. I used it for the first few months of my practise and found myself getting displeased with it. One of the things that I truly appreciate about the Thought Pudding dashboard is the design. *** I look forward to my 'admin hour' - logging a session, a reschedule/cancellation, or payment is just takes a few clicks. As someone who has been wary of incorporating tech (beyond the G-Suite) in my work, this is a tech-solution I can wholeheartedly endorse.*** The Thought Pudding team has also been extremely patient and prompt in troubleshooting any tech concerns/queries that I have had as I got used to the dashboard.",
  },
];

const VerticalTestimonialSection: React.FC = () => {
  const carouselRef = useRef<HTMLDivElement | null>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [isAutoScroll, setIsAutoScroll] = useState(true);
  const router = useRouter();

  // Auto-scroll on mobile
  useEffect(() => {
    const isDesktop = window.innerWidth >= 768;

    if (!isAutoScroll || !carouselRef.current || !isDesktop) return;

    const scroll = () => {
      if (!carouselRef.current) return;
      carouselRef.current.scrollLeft += 1;
    };

    const interval = setInterval(scroll, 20);
    return () => clearInterval(interval);
  }, [isAutoScroll]);

  const scrollToIndex = (index: number) => {
    setIsAutoScroll(false);
    setActiveIndex(index);

    const container = carouselRef.current;
    if (!container) return;

    const cardWidth = container.offsetWidth * 0.65 + 16; // 65% width + gap
    container.scrollTo({
      left: index * cardWidth,
      behavior: "smooth",
    });
  };

  const isIOS = () => {
    if (typeof window !== "undefined") {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) ||
             (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    }
    return false;
  };

  return (
    <div
      className={`w-full bg-[#251D5C] lg:px-[150px] sm:px-6 relative min-h-screen ${isIOS() ? "" : "bg-cover bg-center bg-no-repeat"} flex items-center justify-center`}
      style={{
        backgroundImage: "url('/assets/images/newHome/bg-carousal.png')",
        backgroundSize: isIOS() ? "contain" : "cover",
        backgroundPosition: "center",
        backgroundAttachment: "fixed",
      }}
    >
      <div className="max-w-8xl w-full flex flex-col md:flex-row justify-between items-center md:items-left gap-12">
        {/* Left Content */}
        <div className="md:w-[50%] flex flex-col justify-center md:items-start md:text-left">
         <h2 className="text-white text-center pt-16 px-6 md:px-0 md:pt-4 md:pt-0 md:text-start text-[28px] sm:text-[34px] md:text-[60px] font-bold leading-tight mb-6">
            See what our <br />
            community of{" "}
            <span
              className="inline-block text-[#251D5C] italic font-semibold px-4 py-2 bg-no-repeat bg-contain bg-center"
              style={{
                backgroundImage: "url('/assets/images/newHome/Name-bg3.png')",
              }}
            >
              Therapists
            </span>{" "}
            are saying
          </h2>
          <div className="flex justify-center md:justify-start w-full">
            <button
              className="mt-4 px-5 py-3 bg-white text-[#251D5C] rounded-lg text-sm sm:text-base font-semibold w-auto"
              onClick={() =>
                router.push(
                  "https://calendly.com/thoughtpuddingdemo/making-private-practice-easy-demo-call"
                )
              }
            >
              Join Community
            </button>
          </div>
        </div>

        {/* Right Testimonials */}
        <div className="md:w-[60%] w-full relative overflow-hidden min-h-[520px] flex flex-col">
          {/* Desktop vertical scroll */}
          <div className="hidden md:block absolute top-0 left-0 w-full h-full overflow-visible">
            <div
              className="flex flex-col animate-verticalScroll space-y-8"
            >
              {[...testimonials, ...testimonials].map((item, idx) => {
                const isReverse = idx % 2 !== 0;
                const borderColor = idx % 2 === 0 ? "#514ED8" : "#E27AB1";

                return (
                  <div
                    key={idx}
                    className={`bg-white rounded-xl px-6 pt-12 pb-6 shadow-lg w-full relative flex flex-col md:flex-row ${
                      isReverse ? "md:flex-row-reverse" : ""
                    } items-start gap-6`}
                  >
                    {/* Image */}
                    <div className="relative w-[160px] shrink-0 flex flex-col items-center">
                      <div className="relative w-[180px] h-[180px] -mt-16 z-10">
                        <div
                          className="absolute inset-0 border-r-[3px] border-b-[3px] rounded-[20px] pointer-events-none"
                          style={{
                            borderColor,
                            boxShadow: `8px 8px 12px ${
                              idx % 2 === 0
                                ? "rgba(81, 78, 216, 0.5)"
                                : "rgba(226, 122, 177, 0.5)"
                            }`,
                          }}
                        />
                        <div className="overflow-hidden rounded-[20px] w-full h-full">
                          <Image
                            src={item.image}
                            alt={item.name}
                            width={180}
                            height={180}
                            className="w-full h-full object-cover rounded-[20px]"
                          />
                        </div>
                      </div>
                      <div
                        className="mt-2 w-full text-center bg-no-repeat bg-contain bg-center py-2"
                        style={{
                          backgroundImage: `url('${item.nameBgImage}')`,
                        }}
                      >
                        <div className="text-white font-semibold text-sm">
                          {item.name}
                        </div>
                      </div>
                      <div className="text-black text-xs italic">
                        {item.title}
                      </div>
                    </div>

                    <div className="text-sm text-gray-800 leading-relaxed mt-4 md:mt-0">
                    <p className="mb-2">
                      {item.text.split("***").map((part, i) => {
                        // Use card index (idx) to determine color for all highlights in the card
                        const highlightColor = idx % 2 === 0 ? "#514ED8" : "#E27AB1";

                        return i % 2 !== 0 ? (
                          <span key={i} className="font-medium" style={{ color: highlightColor }}>
                            {part}
                          </span>
                        ) : (
                          part
                        );
                      })}
                    </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Mobile horizontal scroll */}
          <div
            ref={carouselRef}
            className="md:hidden w-full overflow-x-auto flex gap-4 pt-24 pb-12 scroll-smooth no-scrollbar ml-6"
          >
            {testimonials.map((item, idx) => {
              const borderColor = idx % 2 === 0 ? "#514ED8" : "#E27AB1";
              return (
                <div
                  key={idx}
                  className="min-w-[65%] bg-white rounded-xl shadow-lg px-4 pb-6 relative flex flex-col items-center text-center mx-auto overflow-visible"
                  style={{ paddingTop: '150px' }}
                >
                  {/* Image positioned to extend 20% outside the card */}
                  <div className="absolute -top-[20px] left-1/2 transform -translate-x-1/2 w-[160px] h-[160px] z-20">
                    {/* 3D Background Element */}
                    <div
                      className="absolute top-0 left-0 w-full h-full rounded-[10px] z-0"
                      style={{
                        backgroundColor: borderColor,
                        transform: "translateX(-3px) translateY(3px)",
                      }}
                    ></div>

                    <div className="overflow-hidden rounded-[10px] w-full h-full relative z-10">
                      <Image
                        src={item.image}
                        alt={item.name}
                        width={160}
                        height={160}
                        className="w-full h-full object-cover rounded-[10px]"
                      />
                    </div>
                  </div>
                  <div
                    className="mt-2 w-full text-center bg-no-repeat bg-contain bg-center py-1"
                    style={{ backgroundImage: `url('${item.nameBgImage}')` }}
                  >
                    <div className="text-white font-semibold text-xs">
                      {item.name}
                    </div>
                  </div>
                  <div className="text-black text-[10px] italic mb-1">
                    {item.title}
                  </div>
                  <div className="text-xs text-gray-800 px-1 leading-snug">
                  <p className="mb-2">
                      {item.text.split("***").map((part, i) => {
                        const highlightColor = idx % 2 === 0 ? "#514ED8" : "#E27AB1";

                        return i % 2 !== 0 ? (
                          <span key={i} className="font-medium" style={{ color: highlightColor }}>
                            {part}
                          </span>
                        ) : (
                          part
                        );
                      })}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Dots below carousel */}
          <div className="md:hidden flex justify-center items-center mt-2 mb-24 space-x-2">
            {testimonials.map((_, idx) => (
              <button
                key={idx}
                onClick={() => scrollToIndex(idx)}
                className={`transition-all duration-300 rounded-full ${
                  activeIndex === idx
                    ? "bg-white w-4 h-4"
                    : "bg-white/50 w-4 h-4 scale-50"
                }`}
              ></button>
            ))}
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes verticalScroll {
          0% {
            transform: translateY(0%);
          }
          100% {
            transform: translateY(-50%);
          }
        }
        .animate-verticalScroll {
          animation: verticalScroll 60s linear infinite;
        }
        .no-scrollbar::-webkit-scrollbar {
          display: none;
        }
        .no-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </div>
  );
};

export default VerticalTestimonialSection;