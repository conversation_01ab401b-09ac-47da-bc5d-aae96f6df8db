// Using The New Elegance font via CSS class

export default function ClientHeroSection() {
  return (
    <section id="hero" className="pt-24 pb-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center lg:items-start justify-between gap-12">
          {/* Left Text Block (Higher) */}
          <div
            className="w-full lg:w-1/3 text-left mb-8 lg:mb-0"
            style={{ transform: "none" }}
          >
            <h1 className="text-[24px] text-black font-bold font-roboto mb-2">
              When things feel heavy,
            </h1>
            <h2 className="text-[30px] text-[#6F58A5] font-bold font-new-elegance italic">
              starting therapy shouldn&apos;t.
            </h2>
          </div>

          {/* Center Image with Tilt and Ellipse Shadow */}
          <div className="w-full lg:w-1/3 flex justify-center mb-8 lg:mb-0">
            <div className="relative flex flex-col items-center">
              {/* Tilted Image GIF */}
              <div
                className="relative z-10"
                style={{ transform: "rotate(-8deg)" }}
              >
                <img
                  src="/assets/images/client/hero-animation-one.gif"
                  alt="Therapy illustration"
                  width={300}
                  height={300}
                  className="block rounded-lg w-[280px] h-auto md:w-[400px] lg:w-[550px]"
                />
              </div>
              {/* Ellipse Shadow */}
              <div
                className="absolute bg-[#EFEFEF] hidden md:block"
                style={{
                  width: "420px",
                  height: "48px",
                  left: "50%",
                  bottom: "5px",
                  transform:
                    "translateX(-50%) rotate(-8deg) skewX(-25deg)",
                  borderRadius: "50%",
                  zIndex: 0,
                }}
              ></div>
              {/* Mobile shadow */}
              <div
                className="absolute bg-[#EFEFEF] md:hidden"
                style={{
                  width: "230px",
                  height: "24px",
                  left: "50%",
                  bottom: "5px",
                  transform:
                    "translateX(-50%) rotate(-8deg) skewX(-25deg)",
                  borderRadius: "50%",
                  zIndex: 0,
                }}
              ></div>
            </div>
          </div>

          {/* Right Text Block (Lower) */}
          <div
            className="w-full lg:w-1/3 text-left pl-0 lg:pl-8 lg:mt-48"
            style={{ transform: "none" }}
          >
            <h1 className="text-[24px] text-black font-bold font-roboto mb-2">
              At Thought Pudding,
            </h1>
            <h2 className="text-[30px] text-black font-bold font-new-elegance italic">
              we make starting therapy <span className="text-[#6F58A5]">easier.</span>
            </h2>
          </div>
        </div>
      </div>
    </section>
  );
} 