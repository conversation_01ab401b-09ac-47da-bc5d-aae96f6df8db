import axiosInstance from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import toast from "react-hot-toast";

// Define the booking message data interface
export interface BookingMessageData {
  bookingMessage: string;
}

// Define API response interface
export interface ApiResponse<T = unknown> {
  status: string;
  message: string;
  data: T;
  responseCode: number;
}

// Save booking message
export const saveBookingMessage = async (message: string): Promise<ApiResponse<unknown>> => {
  const toastId = toast.loading("Saving booking message...", {
    duration: Infinity,
  });

  try {
    // Create FormData object to match the format used by public-calendar form
    const formData = new FormData();

    // Create an object with only the bookingMessage key
    const onboardingData = {
      bookingMessage: message
    };

    // Convert to JSON string and append to FormData
    const onboardingDataString = JSON.stringify(onboardingData);
    formData.append('onboardingData', onboardingDataString);

    // Using the onboarding endpoint directly with PATCH method
    const url = endpoints.publicCalendar; // This is "/therapist/onboarding"
    const response = await axiosInstance.patch(
      url,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    toast.dismiss(toastId);

    return response.data;
  } catch (error) {
    throw error;
  }
};

// Fetch booking message
export const fetchBookingMessage = async (): Promise<string> => {
  try {
    // Using the onboarding endpoint directly
    const url = endpoints.publicCalendar;
    const response = await axiosInstance.get(url);

    // Extract the booking message from the response
    if (response.data && response.data.status === "success" && Array.isArray(response.data.data) && response.data.data.length > 0) {
      return response.data.data[0]?.bookingMessage || "";
    }

    return "";
  } catch (error) {
    console.error("Error fetching booking message:", error);

    // Return default message if fetch fails
    return "Thanks for booking your session with me. To complete your booking, kindly pay the session fees 48 hours before your session at UPI ID: abc@paytm.....\n\n\nWrite your own";
  }
};
