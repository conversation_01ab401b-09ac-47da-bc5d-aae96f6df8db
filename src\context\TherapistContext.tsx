"use client";

import { useGetSettingData } from "@/services/setting.service";
import { createContext, useContext } from "react";

// Define a type for therapist data
interface TherapistData {
  // Add the properties of therapist data here
  _id: string;
  email: string;
  name: string;
  bankDetails: {
    upiApprove: boolean;
  };
  s3ProfilePhoto: boolean;
  settings: {
    emailNotification: boolean;
    weeklyReportsNotification: boolean;
    emailOnSessionConfirmation: boolean;
  };
  googleCalendarSynced: boolean;
  verificationDetails: {
    uploadedDocsCount: number;
    genderPreference: string[];
    featuresNeed: string[];
    source: string[];
    sentForVerification: boolean;
    docs: string[];
  };
  phone?: string;
  address?: string;
  isVerified: string;
  profilePhoto?: string;
  isDeleted: string;
  menus: {
    paymentTracker: string;
    paymentGateway: string;
  };
  identifier: string;
  createdAt: string;
  updatedAt: string;
}

const TherapistContext = createContext<
{
  therapistData: TherapistData | null;
  refetchTherapistData?: () => Promise<void>;
} | undefined
>(undefined);

export const TherapistProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { therapistData, refetchTherapistData  } = useGetSettingData();

  return (
    <TherapistContext.Provider value={{ therapistData, refetchTherapistData }}>
      {children}
    </TherapistContext.Provider>
  );
};

export const useFetchTherapistData = () => useContext(TherapistContext);
