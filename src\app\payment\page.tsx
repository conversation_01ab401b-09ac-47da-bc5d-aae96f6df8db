"use client";
import Button from "@/components/common/Button";
import DatePicker from "@/components/common/DatePicker";
import Input from "@/components/common/Input";
import TablePagination from "@/components/common/TablePagination";
import Tabs from "@/components/common/Tabs";
import ActivityCard from "@/components/dashboard/common/ActivityCard";
import PaymentTBody, {
  Item,
} from "@/components/dashboard/common/table/PaymentTBody";
import THeader from "@/components/dashboard/common/table/THeader";
import CommonModal from "@/components/dashboard/CommonModal";
import DaysSelectDropdown from "@/components/dashboard/DaysSelectDropdown";
import DashboardLayout from "@/layout/dashboard/DashboardLayout";
import {
  cancellationCharge,
  downloadPaymentTracker,
  FilterParams,
  getTemplateTest,
  updatePayment,
  updateSessionAmount,
  updateStatus,
  useGetPayments,
  useGetPaytracker,
} from "@/services/payment.service";
import { useGetSettingData } from "@/services/setting.service";
import { fetcher, formatDate } from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import { FunnelSimple, MagnifyingGlass, X } from "@phosphor-icons/react";
import moment from "moment";
import { useRouter } from "next/navigation";
import { useEffect, useState, useCallback, useRef } from "react";
import { mutate } from "swr";
import { toast } from "react-hot-toast";

const sessionTabs = [
  { label: "Still Pending", value: "stillPending" },
  { label: "Paid On Time", value: "paidOnTime" },
  { label: "Paid Delayed", value: "paidDelayed" },
  { label: "Cancelled", value: "cancelled" },
  { label: "All", value: "" },
];

const paymentTableHeader = [
  "Name",
  "Session date",
  "status",
  "Amount",
  "Mark as",
  "Payment on",
  "actions",
];

interface singlePaymentData {
  clientId?: {
    _id: string;
    name?: string;
    email?: string;
  };
  _id?: string;
  name?: string;
  email?: string;
  tillDate?: Date;
  fromDate?: Date;
  sessionDate?: Date;
}

interface TemplateData {
  receiverData?: {
    name: string;
    email: string;
  };
  emailSubject?: string;
  emailBody?: string;
}

interface PaymentTrackerData {
  collected_payment?: {
    data: number;
    change: number;
  };
  pending_payment?: {
    data: number;
    change: number;
  };
  total_earnings?: {
    data: number;
    change: number;
  };
}

const Payment = () => {
  const router = useRouter();

  const [clientId, setClientId] = useState<string | null>(null);
  const [isMonthsDropSelect, setIsMonthsDropSelect] = useState("Today");
  const [isReminderModal, setIsReminderModal] = useState(false);
  const [isCancelSessionModal, setIsCancelSessionModal] = useState(false);
  const [isUpdatePayment, setIsUpdatePayment] = useState(false);
  const [activeTable, setActiveTable] = useState(sessionTabs[0]);
  const [isResetting, setIsResetting] = useState(false);
  const [iscancelling, setIscancelling] = useState(false);
  const [loading, setLoading] = useState(false); // Loader state
  const [isExporting, setIsExporting] = useState(false); // Export loader state
  const [startDate, setStartDate] = useState(() => {
    const start = new Date();
    start.setHours(0, 0, 0, 0); // Set to start of the day (00:00:00)
    return start;
  });

  const [endDate, setEndDate] = useState(() => {
    const end = new Date();
    end.setHours(23, 59, 59, 999); // Set to end of the day (23:59:59)
    return end;
  });

  const [singlePaymentData, setSinglePaymentData] = useState<singlePaymentData>(
    {}
  );

  const [isFilterApplied, setIsFilterApplied] = useState(false);

  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");

  const [isFilter, setIsFilter] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  const [selectedStatus, setSelectedStatus] = useState("custom"); // default to custom date
  const [searchStartDate, setSearchStartDate] = useState<string | null>(null);
  const [searchEndDate, setSearchEndDate] = useState<string | null>(null);
  const [paymentStatus] = useState("");
  const [cancellationChargeID, setCancellationChargeID] = useState("");
  const [chargeAmount, setChargeAmount] = useState<string | "">("");

  const [isUpdating, setIsUpdating] = useState(false);
  const [newAmount, setNewAmount] = useState<string | "">("");
  const [paymentID, setPaymentID] = useState("");
  const [isSessionAmountUpdate, setIsSessionAmountUpdate] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<string | null>(null);

  // Function to update selected option after successful amount update
  const updateSelectedOptionRef = useRef<((itemId: string, option: string) => void) | null>(null);

  const handleUpdateSelectedOption = useCallback((updateFn: (itemId: string, option: string) => void) => {
    updateSelectedOptionRef.current = updateFn;
  }, []);

  const [filterparams, setFilterparams] = useState<FilterParams>({});
  const pageSize = 5; // Set the page size as required

  const query =
    `pageSize=${pageSize}&pageNumber=${currentPage}&${
      activeTable?.value
    }=true&searchText=${debouncedSearchText}&clientName=${clientId ?? ""}` +
    ((filterparams.searchStartDate && filterparams.searchEndDate) ||
    filterparams.paymentStatus
      ? `&startDate=${filterparams.searchStartDate}&endDate=${filterparams.searchEndDate}&${filterparams.paymentStatus}=true`
      : "");

  // get payment listing
  const { paymentData, paymentLoading, paymentCount } = useGetPayments({
    pageSize,
    currentPage,
    activeTable: activeTable?.value,
    debouncedSearchText,
    filterparams,
    clientId: clientId ?? "", // Convert null to undefined
  });

  const { therapistData } = useGetSettingData();

  const totalPages = Math.ceil(paymentCount / pageSize);

  // Reset page to 1 when the active tab changes
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTable, isFilterApplied, filterparams]);

  // get payment activity data
  const { paymentTrackerData, mutatePaymentTrackerData } = useGetPaytracker(
    startDate,
    endDate
  ) as {
    paymentTrackerData: PaymentTrackerData;
    mutatePaymentTrackerData: () => void;
  };

  const activity = [
    {
      title: "Received",
      session: `₹${paymentTrackerData?.collected_payment?.data ?? 0}`,
      percentage: `${paymentTrackerData?.collected_payment?.change ?? 0}`,
    },
    {
      title: "Pending",
      session: `₹${paymentTrackerData?.pending_payment?.data ?? 0}`,
      percentage: `${paymentTrackerData?.pending_payment?.change ?? 0}`,
    },
    {
      title: "Total Earnings",
      session: `₹${paymentTrackerData?.total_earnings?.data ?? 0}`,
      percentage: `${paymentTrackerData?.total_earnings?.change ?? 0}`,
    },
  ];

  // set search value
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchText(searchText);
      setCurrentPage(1);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchText]);

  async function getTemplateBodyData() {
    try {
      setLoading(true); // Start loader
      const response = (await getTemplateTest(
        singlePaymentData?.clientId?._id ?? "",
        singlePaymentData?._id ?? ""
      )) as { data: TemplateData };
      if (response) {
        console.log(response);
      }
    } catch (error) {
      console.error("Failed to fetch template body data.", error);
    } finally {
      setLoading(false); // Stop loader
    }
  }

  const handleStatusChange = (status: string) => {
    console.log("come into loop");
    setSelectedStatus(status);

    // Get today's date
    const today = new Date();

    // Clear start and end dates if another option is selected
    if (status !== "custom") {
      let startDate, endDate;

      switch (status) {
        case "last7":
          startDate = new Date(today);
          startDate.setDate(today.getDate() - 7);
          endDate = today;
          break;
        case "last15":
          startDate = new Date(today);
          startDate.setDate(today.getDate() - 15);
          endDate = today;
          break;
        case "last30":
          startDate = new Date(today);
          startDate.setDate(today.getDate() - 30);
          endDate = today;
          break;
        default:
          startDate = null;
          endDate = null;
      }

      // Convert to ISO string with timezone
      const isoStartDate = startDate ? startDate.toISOString() : null;
      const isoEndDate = endDate ? endDate.toISOString() : null;

      setSearchStartDate(isoStartDate); // Set the calculated start date in ISO format
      setSearchEndDate(isoEndDate); // Set the calculated end date in ISO format
    } else {
      // If "custom" is selected, clear the dates
      setSearchStartDate(null);
      setSearchEndDate(null);
    }
  };

  const handleApplySearchFilter = () => {
    // Set filterParams to valuen trigger a new API call

    setFilterparams({
      searchStartDate: searchStartDate
        ? moment(searchStartDate).startOf("day").toISOString()
        : "",
      searchEndDate: searchEndDate
        ? moment(searchEndDate).endOf("day").toISOString()
        : "",
      paymentStatus,
    });

    // show reset filter
    setIsFilterApplied(true);

    // Close the modal after applying the filter
    setIsFilter(false);
  };

  // resetFilter
  const resetFilter = useCallback(() => {
    setIsResetting(true); // Start reset process
    // Reset search-related states
    if (clientId) {
      setClientId(null);

      router.push(`/payment`);

      // const url = `${endpoints.paymentTracker.getPaymentsByTherapist}?${query}`;
      // mutate(url, async () => {
      //   await fetcher(url);
      // });
    }

    setSelectedStatus("custom");
    setSearchStartDate(null);
    setSearchEndDate(null);
    setSearchText("");
    setFilterparams({});
    setIsFilterApplied(false); // Hide Reset button
  }, [clientId, router]);

  useEffect(() => {
    if (!isResetting && typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      const newClientId = searchParams.get("client");

      // Update clientId if it has changed
      if (newClientId !== clientId) {
        setClientId(newClientId);
      }
    }
  }, [router, clientId, resetFilter, isResetting]);

  useEffect(() => {
    if (clientId) {
      setIsFilterApplied(true);
      // Check if paymentData[0]?.clientId?.email is defined before setting it
      if (
        (paymentData[0] as { clientId?: { name: string } })?.clientId?.name &&
        !searchText
      ) {
        // setSearchText(paymentData[0]?.clientId?.email);
        setSearchText(
          (paymentData[0] as { clientId?: { name: string } })?.clientId?.name ||
            ""
        );
      }
    } else if (!filterparams) {
      setIsFilterApplied(false);
    }
  }, [clientId, paymentData, searchText, filterparams]);

  // Handle export functionality
  const handleExport = async () => {
    try {
      setIsExporting(true);

      // Determine which status to export based on active tab
      const exportParams: {
        stillPending?: boolean;
        paidOnTime?: boolean;
        paidDelayed?: boolean;
        cancelled?: boolean;
        startDate?: string;
        endDate?: string;
        searchText?: string;
        clientName?: string;
      } = {};

      // Set status based on active tab
      if (activeTable?.value === "stillPending") {
        exportParams.stillPending = true;
      } else if (activeTable?.value === "paidOnTime") {
        exportParams.paidOnTime = true;
      } else if (activeTable?.value === "paidDelayed") {
        exportParams.paidDelayed = true;
      } else if (activeTable?.value === "cancelled") {
        exportParams.cancelled = true;
      } else {
        // For "All" tab, include all statuses
        exportParams.stillPending = true;
        exportParams.paidOnTime = true;
        exportParams.paidDelayed = true;
        exportParams.cancelled = true;
      }

      // Only add date filters if they are applied to the table
      // This matches the logic used in the table query (lines 145-148)
      if (filterparams.searchStartDate && filterparams.searchEndDate) {
        exportParams.startDate = filterparams.searchStartDate;
        exportParams.endDate = filterparams.searchEndDate;
      }
      // If no date filters are applied, export all data without date restrictions
      // This ensures the export matches what's displayed in the table

      // Add search parameters to match the table query
      if (debouncedSearchText) {
        exportParams.searchText = debouncedSearchText;
      }
      if (clientId) {
        exportParams.clientName = clientId;
      }

      await downloadPaymentTracker(exportParams);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="bg-white mt-5 rounded-base overflow-hidden">
        {/* activity section */}
        <div className="p-5">
          <div className="flex items-center justify-between">
            <h2 className="text-lg/5 sm:text-xl/6 text-primary font-semibold">
              Activities
            </h2>
            <DaysSelectDropdown
              value={isMonthsDropSelect}
              onChange={(value: unknown) =>
                setIsMonthsDropSelect(value as string)
              }
              DropClass=""
              setStartDate={setStartDate}
              setEndDate={setEndDate}
            />
          </div>
          <div className="pt-5 grid sm:grid-cols-3 gap-5">
            {activity?.map((items, index) => (
              <ActivityCard
                key={index}
                title={items.title}
                count={items.session}
                percentage={items.percentage}
                borderColor={
                  index === 0
                    ? "border-[#48A400]"
                    : index === 1
                    ? "border-[#1339FF]"
                    : index === 2
                    ? "border-[#FF5C00]"
                    : ""
                }
              />
            ))}
          </div>
        </div>

        {/* table */}
        <div className="p-5">
          <div className="flex flex-wrap gap-5 items-center justify-between">
            <Tabs
              tabs={sessionTabs}
              activeTab={activeTable?.label}
              setActiveTab={(tab) => setActiveTable(tab)}
              sessionCount={paymentCount}
            />
            <div className="flex gap-2">
              <div className="flex items-center gap-2 max-w-[391px] sm:min-w-[391px] w-full sm:py-15px sm:px-5 p-3 border border-[#9B9DB7] rounded-full text-xs text-primary">
                <MagnifyingGlass className="text-primary/50 min-w-5 w-5 h-5" />
                <input
                  type="search"
                  value={searchText}
                  placeholder="Search your client name and id"
                  className="outline-none w-full placeholder:text-primary/50"
                  onChange={(e) => setSearchText(e.target.value)}
                />
                <span className="text-primary/50">|</span>
                <div className="flex items-center bg-green-600/5 py-1 px-2.5 rounded-full gap-3">
                  <FunnelSimple
                    size={20}
                    className="text-green-600 cursor-pointer"
                    onClick={() => {
                      setIsFilter(!isFilter);
                    }}
                  />
                  {isFilterApplied && (
                    <div
                      onClick={resetFilter}
                      className="w-5 h-5 rounded-full border border-primary/20 text-primary flex items-center justify-center bg-white cursor-pointer"
                    >
                      <X size={12} />
                    </div>
                  )}
                </div>
              </div>

              <button
                className={`flex items-center gap-2 max-h-[58px] h-[58px] max-w-[104px] sm:min-w-[104px] w-full sm:py-15px sm:px-5 p-3 border border-[#9B9DB7] rounded-full text-xs text-primary ${
                  isExporting ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
                }`}
                onClick={handleExport}
                disabled={isExporting}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="17"
                  height="16"
                  viewBox="0 0 17 16"
                  fill="none"
                >
                  <path
                    d="M11.8299 5.67493C14.5299 5.90743 15.6324 7.29493 15.6324 10.3324V10.4299C15.6324 13.7824 14.2899 15.1249 10.9374 15.1249H6.05486C2.70236 15.1249 1.35986 13.7824 1.35986 10.4299V10.3324C1.35986 7.31743 2.44736 5.92993 5.10236 5.68243"
                    stroke="#2D3134"
                    stroke-opacity="0.7"
                    stroke-width="1.125"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M8.5 10.2501V1.71509"
                    stroke="#2D3134"
                    stroke-opacity="0.7"
                    stroke-width="1.125"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M11.0123 3.3875L8.49979 0.875L5.9873 3.3875"
                    stroke="#2D3134"
                    stroke-opacity="0.7"
                    stroke-width="1.125"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>{" "}
                <p className="text-[#2D3134B2]">
                  {isExporting ? "Exporting..." : "Export"}
                </p>
              </button>
            </div>
          </div>
          <div className="pt-10">
            <div className="w-full border border-green-600/25 rounded-base overflow-hidden">
              <div className="overflow-x-auto relative">
                <table className="w-full  bg-white">
                  <THeader data={paymentTableHeader} />
                  <PaymentTBody
                    TableData={paymentData}
                    paymentLoading={paymentLoading}
                    setIsReminderModal={setIsReminderModal}
                    isReminderModal={isReminderModal}
                    setIsCancelSessionModal={setIsCancelSessionModal}
                    isCancelSessionModal={isCancelSessionModal}
                    setCancellationChargeID={setCancellationChargeID}
                    urlClientId={clientId ?? ""}
                    setSinglePaymentData={(item: Item) =>
                      setSinglePaymentData(item as unknown as singlePaymentData)
                    }
                    query={query}
                    mutatePaymentTrackerData={mutatePaymentTrackerData} // Pass the mutate function
                    setPaymentID={setPaymentID}
                    setIsUpdatePayment={setIsUpdatePayment}
                    isUpdatePayment={isUpdatePayment}
                    setIsSessionAmountUpdate={setIsSessionAmountUpdate}
                    setPendingStatus={setPendingStatus}
                    therapistData={therapistData}
                    onUpdateSelectedOptionRef={handleUpdateSelectedOption}
                  />
                </table>
              </div>
              <TablePagination
                totalPages={totalPages}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            </div>
          </div>
        </div>
      </div>
      {/* ====== Session modals ====== */}
      {/* Filter Modal */}
      <CommonModal title="Filter" isClose={isFilter} setIsClose={setIsFilter}>
        <div className="py-7.5">
          <p className="text-base/5 text-primary font-medium">Client Status</p>
          <div className="space-y-2.5 pt-3">
            {["last7", "last15", "last30", "custom"].map((option) => (
              <label
                key={option}
                className="flex items-center gap-2.5 text-sm/5 text-gray-500"
              >
                <input
                  type="radio"
                  name="Status"
                  className="w-4.5 h-4.5"
                  checked={selectedStatus === option}
                  onChange={() => handleStatusChange(option)}
                />
                {option === "custom"
                  ? "Custom Date"
                  : `Last ${
                      option === "last7"
                        ? "7"
                        : option === "last15"
                        ? "15"
                        : "30"
                    } Days`}
              </label>
            ))}
          </div>

          {selectedStatus === "custom" && (
            <div className="space-y-5 pt-6">
              <div>
                <label className="text-base/5 text-primary font-medium">
                  Start Date
                </label>
                <DatePicker
                  value={searchStartDate ?? ""}
                  placeholder={`DD/MM/YYYY`}
                  className={`!mt-3`}
                  onChange={(date) => {
                    setSearchStartDate(date);
                  }}
                />
              </div>
              <div>
                <label className="text-base/5 text-primary font-medium">
                  End Date
                </label>
                <DatePicker
                  value={searchEndDate ?? ""}
                  minDate={searchStartDate ?? ""}
                  placeholder={`DD/MM/YYYY`}
                  className={`!mt-3`}
                  onChange={(date) => {
                    setSearchEndDate(date);
                  }}
                />
              </div>
            </div>
          )}

          {/* <p className="text-base/5 text-primary font-medium pt-6">
            Payment Mark As
          </p>
          <div className="flex items-center gap-6 pt-3">
            {["paidOnTime", "stillPending", "paidDelayed"].map(
              (paymentOption) => (
                <label
                  key={paymentOption}
                  className="flex items-center gap-2.5 text-sm/5 text-gray-500"
                >
                  <input
                    type="radio"
                    name="Payment"
                    className="w-4.5 h-4.5"
                    onChange={() => setPaymentStatus(paymentOption)}
                  />
                  {paymentOption === "paidOnTime"
                    ? "Paid On Time"
                    : paymentOption === "stillPending"
                    ? "Still Pending"
                    : "Paid Delayed"}
                </label>
              )
            )}
          </div> */}
        </div>
        <div className="flex items-center justify-end gap-3.5">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() => {
              setIsFilter(false);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px]`}
            disabled={
              selectedStatus === "custom" &&
              (!searchStartDate || !searchEndDate)
            }
            onClick={handleApplySearchFilter}
          >
            Apply
          </Button>
        </div>
      </CommonModal>

      {/* Reminder massage Modal */}
      <CommonModal
        title="Send reminder"
        isClose={isReminderModal}
        setIsClose={(isClose) => {
          if (!loading) {
            setIsReminderModal(isClose); // Prevent manual close while loading
          }
        }}
        className={`!max-w-[450px]`}
      >
        <div className="text-base/7 text-primary pt-5">
          <p>
            To:{" "}
            <span className="text-green-600">{`${
              singlePaymentData?.clientId?.name || "-"
            } <${singlePaymentData?.clientId?.email || "-"}>`}</span>
          </p>
          <p>
            Subject: <span className="font-semibold">Payment Reminder 🔔</span>
          </p>
          <p className="text-green-600 font-semibold capitalize">
            Hi {singlePaymentData?.clientId?.name || "-"}
          </p>
          <p className="pt-5">
            This is to remind you that your payment for session on{" "}
          </p>
          <p>
            <span className="font-semibold">
              {singlePaymentData &&
                formatDate(
                  moment(singlePaymentData.sessionDate || "").format(
                    "YYYY-MM-DD"
                  )
                )}{" "}
              at{" "}
              {singlePaymentData &&
                moment(singlePaymentData.sessionDate || "").format("HH:mm A")}
            </span>{" "}
            with <span className="font-semibold">{therapistData?.name}</span>{" "}
            has been pending for sometime. Kindly clear it before your next
            session.
          </p>
        </div>
        <Button
          variant="filledGreen"
          className={`w-full mt-7.5 ${
            loading ? "opacity-50 cursor-not-allowed" : ""
          }`}
          onClick={() => {
            if (!loading) {
              getTemplateBodyData().then(() => {
                setIsReminderModal(false); // Close modal after successful API call
              });
            }
          }}
          disabled={loading} // Disable button during loading
        >
          {loading ? "Processing..." : "Send Reminder"} {/* Loader text */}
        </Button>
      </CommonModal>

      {/* Cancel Session */}
      <CommonModal
        title="Cancel Session"
        isClose={isCancelSessionModal}
        setIsClose={setIsCancelSessionModal}
      >
        <p className="text-base/6 text-primary/70 max-w-[465px] pt-6">
          Enter the amount you want to charge to cancel this session (in
          Rupees).
        </p>
        <div className="pt-5">
          <label className="text-base/5 text-primary font-medium">
            Enter Cancellation Charge
          </label>
          <Input
            value={chargeAmount}
            onChange={(e) => {
              const value = e.target.value;

              // Allow empty input for smooth editing
              if (
                value === "" ||
                (Number(value) > 0 && !value.startsWith("-"))
              ) {
                setChargeAmount(value);
              }
            }}
            name="charge"
            type="number"
            placeholder="Enter Amount"
            icon="rup"
          />
        </div>
        <div className="flex justify-end pt-6">
          <Button
            variant="filledGreen"
            onClick={async () => {
              if (chargeAmount) {
                setIscancelling(true);
                try {
                  const formData = {
                    charge: chargeAmount,
                  };

                  // Call the cancellationCharge API with the provided ID and charge amount
                  await cancellationCharge(cancellationChargeID, formData);

                  // Call updateStatus API
                  await updateStatus(cancellationChargeID, "Paid Cancellation"); // Or use the appropriate status

                  // Fetch updated payments data after cancellation
                  const url = `${endpoints.paymentTracker.getPaymentsByTherapist}?${query}`;
                  await mutate(url, async () => {
                    await fetcher(url);
                    if (mutatePaymentTrackerData) {
                      await mutatePaymentTrackerData();
                    }
                  });

                  // Show success message for cancellation fee update
                  toast.success("Status updated successfully!");

                  // Close the modal after processing
                  setIsCancelSessionModal(false);

                  setChargeAmount("");
                } catch (error) {
                  console.error("Payment update failed:", error);
                } finally {
                  setIscancelling(false);
                }
              } else {
                // Optionally handle case where chargeAmount is not provided
                console.warn("Please enter a valid cancellation charge.");
              }
            }}
            disabled={iscancelling}
          >
            {iscancelling ? "cancelling..." : "Cancel Session"}
          </Button>
        </div>
      </CommonModal>

      {/* Upadate Payment Modal */}
      <CommonModal
        title={isSessionAmountUpdate ? "Update Session Amount" : "Update Payment"}
        isClose={isUpdatePayment}
        setIsClose={(value) => {
          setIsUpdatePayment(value);
          if (!value) {
            setIsSessionAmountUpdate(false);
            setPendingStatus(null);
            setNewAmount("");
          }
        }}
      >
        <div className="py-7.5 space-y-5">
          <div>
            <label className="text-base/5 text-primary font-medium">
              {isSessionAmountUpdate ? "Enter Session Amount" : "Add Payment"}
            </label>
            <Input
              value={newAmount}
              onChange={(e) => setNewAmount(e.target.value)}
              name="newAmount"
              type="number"
              placeholder="0"
              icon="rup"
            />
          </div>
        </div>
        <div className="flex items-center justify-end gap-3.5">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() => {
              setIsUpdatePayment(false);
              setIsSessionAmountUpdate(false);
              setPendingStatus(null);
              setNewAmount("");
            }}
            disabled={isUpdating}
          >
            Cancel
          </Button>
          <Button
            variant="filledGreen"
            className="min-w-[157px]"
            onClick={async () => {
              if (newAmount) {
                setIsUpdating(true);
                try {
                  if (isSessionAmountUpdate) {
                    // Update session amount
                    await updateSessionAmount(paymentID, newAmount);
                    // Update status if there's a pending status
                    if (pendingStatus) {
                      await updateStatus(paymentID, pendingStatus);
                      // Update the selected option in the dropdown
                      if (updateSelectedOptionRef.current) {
                        updateSelectedOptionRef.current(paymentID, pendingStatus);
                      }
                    }
                  } else {
                    // Update payment
                    const formData = { amount: newAmount };
                    await updatePayment(paymentID, formData);
                  }

                  const url = `${endpoints.paymentTracker.getPaymentsByTherapist}?${query}`;

                  mutate(url, async () => {
                    await fetcher(url);
                  });

                  setIsUpdatePayment(false);
                  setIsSessionAmountUpdate(false);
                  setPendingStatus(null);
                  setNewAmount(""); // Clear input field after update
                } catch (error) {
                  console.error(isSessionAmountUpdate ? "Session amount update failed:" : "Payment update failed:", error);
                } finally {
                  setIsUpdating(false);
                }
              }
            }}
            disabled={isUpdating}
          >
            {isUpdating ? "Updating..." : "Update"}
          </Button>
        </div>
      </CommonModal>
    </DashboardLayout>
  );
};

export default Payment;
