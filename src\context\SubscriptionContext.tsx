// src/context/AuthContext.js
"use client";

import { checkTherapistSubscription } from "@/services/setting.service";
import { usePathname } from "next/navigation";
import { createContext, useContext, useEffect, useState } from "react";
import { useAuth } from "./AuthContext";

type SubscriptionDetails = {
  valid?: boolean;
  validDate?: string
  // Add other properties as needed
};

const SubscriptionContext = createContext<
  | {
      subscriptionDetails: SubscriptionDetails | null; // Use the defined type
      setSubscriptionDetails: React.Dispatch<
        React.SetStateAction<SubscriptionDetails | null>
      >; // Use the defined type
    }
  | undefined
>(undefined);

export const SubscriptionProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const auth = useAuth();
  const pathname = usePathname();
  const [subscriptionDetails, setSubscriptionDetails] = useState<SubscriptionDetails | null>(null);

  useEffect(() => {
    async function checkSubscriptionValid() {
      if (!auth || !auth.isAuthenticated) return null; // Check for undefined and then access isAuthenticated
      if (pathname === "/setting/subscription") {
        // setShow(false);
        setSubscriptionDetails({ valid: true, ...subscriptionDetails || {} });
        return true;
      }
      await checkTherapistSubscription()
        .then((res) => {
          if (res.status === 200) {
            setSubscriptionDetails(res.data);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    }
    checkSubscriptionValid();
  }, [auth, pathname]);

  return (
    <SubscriptionContext.Provider
      value={{ subscriptionDetails, setSubscriptionDetails }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscriptionData = () => useContext(SubscriptionContext);
