export type FormErrors = {
  [key: string]: string | FormErrors | undefined;
};

export const scrollToError = (errors: FormErrors) => {
  const firstError = Object.keys(errors)[0];
  if (!firstError) return;
  
  const element = document.querySelector(`[name="${firstError}"]`);
  if (element) {
    setTimeout(() => {
      const headerOffset = 100;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });

      (element as HTMLElement).focus();
      
      element.classList.add('error-highlight');
      setTimeout(() => {
        element.classList.remove('error-highlight');
      }, 1000);
    }, 100);
  }
};

