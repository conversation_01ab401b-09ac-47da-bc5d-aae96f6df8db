import axiosInstance from "@/utils/axios";
import endpoints from "@/utils/endpoints";

interface TherapistProfileData {
  name?: string;
  phone?: string;
  email?: string;
  panCard?: string;
  gstNumber?: string;
  address?: {
    streetAddress?: string;
    pincode?: string;
    district?: string;
    state?: string;
  };
  therapist_name?: string;
  practice_name?: string;
  linkedin_url?: string;
  hasUsedTrial?: boolean;
  bankDetails?: {
    bankAccountNo?: string;
    ifscCode?: string;
    branch?: string;
    bankName?: string;
    accountHolderName?: string;
    upiId?: string;
    upiApprove?: boolean;
  };
  s3ProfilePhoto?: boolean;
  settings?: {
    emailNotification?: boolean;
    weeklyReportsNotification?: boolean;
    emailOnSessionConfirmation?: boolean;
  };
  googleCalendarSynced?: boolean;
  otpData?: {
    otp?: string;
    validTill?: Date;
  };
  verificationDetails?: {
    uploadedDocsCount?: number;
    agePreference?: string;
    genderPreference?: string[];
    practicingTitle?: string;
    clientLoad?: string;
    yearsOfExperience?: string;
    featuresNeed?: string[];
    source?: string[];
    sentForVerification?: boolean;
    docs?: string[];
  };
  isVerified?: boolean;
  isDeleted?: boolean;
  syncDate?: Date;
  menus?: {
    paymentTracker?: boolean;
    paymentGateway?: boolean;
  };
}


export class AuthService {
  static async signInWithGoogle() {
    return await axiosInstance.get(endpoints.login);
  }

  static async verifyTherapist(code: string) {
    return await axiosInstance.post(
      `${endpoints.verifyTherapist}?code=${code}`
    );
  }

  static async createTherapist(code: string, scope: string) {
    return await axiosInstance.post(
      endpoints.createTherapist + "?code=" + code + "&scope=" + scope
    );
  }

  static async getTherapistVerificationData(th_id: string) {
    return await axiosInstance.get(
      `${endpoints.getTherapistVerificationData}/${th_id}`,
      {
        headers: {
          Authorization: sessionStorage.getItem("tempAuthKey"),
        },
      }
    );
  }

  static async updateTherapistProfile(
    th_id: string,
    data: TherapistProfileData
  ) {
    return await axiosInstance.put(
      `${endpoints.updateTherapist}/${th_id}`,
      data,
      {
        headers: {
          Authorization: sessionStorage.getItem("tempAuthKey"),
        },
      }
    );
  }
}
