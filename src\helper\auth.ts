const Auth = {
  isAuthenticated: false,
  authenticate() {
    if (
      typeof window !== "undefined" &&
      typeof localStorage !== "undefined" &&
      localStorage.getItem("authKeyTh")
    ) {
      this.isAuthenticated = true;
    }
  },
  signout() {
    if (
      typeof window !== "undefined" &&
      typeof localStorage !== "undefined" &&
      localStorage.getItem("authKeyTh")
    ) {
      this.isAuthenticated = false;
      localStorage.removeItem("authKeyTh");
    }
  },
  getAuth() {
    return this.isAuthenticated;
  },
  checkAuth() {
    this.authenticate();
    return this.isAuthenticated;
  },
};

export default Auth;
