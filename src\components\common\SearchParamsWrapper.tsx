"use client";

import { Suspense, ReactNode } from "react";

interface SearchParamsWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
}

// Default fallback component
function DefaultFallback() {
  return (
    <div className="animate-pulse">
      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
    </div>
  );
}

export default function SearchParamsWrapper({ 
  children, 
  fallback = <DefaultFallback /> 
}: SearchParamsWrapperProps) {
  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  );
}
