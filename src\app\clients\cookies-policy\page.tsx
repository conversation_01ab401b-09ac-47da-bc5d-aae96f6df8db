"use client";
import React from "react";
import ClientLayout from "@/layout/client/ClientLayout";
import Link from "next/link";

export default function CookiesPolicyPage() {
  return (
    <ClientLayout>
      <div className="min-h-screen bg-white">
        <div className="max-w-4xl mx-auto px-4 py-28">
          <div className="prose prose-lg max-w-none">
            <h1 className="text-4xl font-bold text-gray-900 mb-8">Cookies Policy</h1>
            
            {/* Redirect Notice */}
            <div className="text-center">
              <p className="text-lg text-gray-700 mb-8">
                Our cookies policy is included as part of our Privacy Policy.
              </p>
              
              <Link 
                href="/clients/privacy-policy#cookies" 
                className="inline-block bg-blue-600 text-white px-8 py-4 rounded-lg hover:bg-blue-700 transition-colors font-medium text-lg"
              >
                View Cookies Policy in Privacy Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </ClientLayout>
  );
} 