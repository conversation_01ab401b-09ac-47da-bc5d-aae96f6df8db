import Image from "next/image";

export default function QuestionBubblesSection() {
  return (
    <section id="questions" className="relative py-16 lg:bg-white bg-[#FDF3DD] overflow-hidden">
      {/* Top Parabolic Curve */}
      <div className="absolute top-0 left-0 right-0 h-28 bg-white" style={{
        clipPath: "ellipse(150% 100% at 50% 100%)"
      }} />
      <div className="max-w-7xl mx-auto px-4">
        {/* Mobile Layout */}
        <div className="lg:hidden">
          <div className="relative flex flex-col items-center gap-6 px-2 min-h-[1000px] pb-28">
            {/* Mobile heading text */}
            <div className="text-center mb-8 z-10 mt-20">
              <h2 className="text-[30px] md:text-[35px] font-bold text-[#6F58A5] leading-tight font-new-elegance">
                When you
                <span className="italic block">finally decide to begin,</span>
                <span className="text-black font-roboto text-[24px] md:text-xl block mt-2">you&apos;re often wondering:</span>
              </h2>
            </div>
            {/* Bubbles stacked vertically (large size) */}
            <div className="flex flex-col items-center gap-4 w-full z-10">
              {/* Top Question Bubble */}
              <div className="relative flex flex-col items-center min-w-[324px] min-h-[324px]">
                <Image
                  src="/assets/images/client/question_bubble_top.svg"
                  alt="Question bubble"
                  width={360}
                  height={360}
                  className="w-[360px] h-[360px] mx-auto"
                />
                <div className="absolute top-[60%] left-1/2 -translate-x-1/2 -translate-y-1/2 text-center w-full px-2">
                  <p className="text-base font-small text-black leading-tight max-w-[200px] mx-auto italic">
                    Last time didn&apos;t go well
                    <span className="font-bold text-medium block mt-1">how can I start better now?</span>
                  </p>
                </div>
              </div>
              {/* Bottom Left Bubble */}
              <div className="relative flex flex-col items-center min-w-[324px] min-h-[324px]">
                <Image
                  src="/assets/images/client/question_bubble_bottom_left.svg"
                  alt="Question bubble"
                  width={324}
                  height={324}
                  className="w-[324px] h-[324px] mx-auto"
                />
                <div className="absolute bottom-24 left-[45%] transform -translate-x-1/2 text-center">
                  <p className="text-base font-semibold text-white leading-tight max-w-[180px] mx-auto italic">
                    How to find the
                    <span className="font-bold text-lg block mt-1">Right therapist?</span>
                  </p>
                </div>
              </div>
              {/* Bottom Right Bubble */}
              <div className="relative flex flex-col items-center min-w-[324px] min-h-[324px]">
                <Image
                  src="/assets/images/client/question_bubble_bottom_right.svg"
                  alt="Question bubble"
                  width={324}
                  height={324}
                  className="w-86 h-86"
                />
                <div className="absolute top-[60%] left-[51%] transform -translate-x-1/2 text-center w-full px-2">
                  <p className="text-sm font-semibold text-black leading-tight max-w-[180px] mx-auto italic">
                    How does
                    <span className="font-bold text-base block mt-1">therapy work?</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          {/* Mobile Bottom Curve */}
          <div className="absolute bottom-0 left-0 right-0 h-28 bg-white" style={{
            clipPath: "ellipse(50% 100% at 50% 100%)"
          }} />
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:block">
          <div className="relative w-full h-[800px] pb-28">
            {/* Left side heading text */}
            <div className="absolute left-8 top-1/2 transform -translate-y-1/2 z-10 mt-12">
              <h2 className="text-[35px] md:text-[45px] lg:text-[45px] font-bold text-[#6F58A5] leading-tight max-w-lg font-new-elegance">
                When you
                <span className="italic block">finally decide to begin,</span>
                <span className="text-black font-medium font-roboto text-2xl md:text-3xl lg:text-4xl">you&apos;re often wondering:</span>
              </h2>
            </div>

            {/* Background SVG - Desktop Only */}
            <div className="absolute inset-0 z-10 flex items-center justify-end pr-25">
              <Image
                src="/assets/images/client/question_bubble_bg.svg"
                alt="Background decoration"
                width={900}
                height={900}
                className="w-auto h-auto max-w-none"
              />
            </div>

            {/* Question Bubbles */}
            <div className="absolute inset-0 z-20">
              {/* Top Question Bubble */}
              <div className="absolute top-16 right-40">
                <div className="relative">
                  <Image
                    src="/assets/images/client/question_bubble_top.svg"
                    alt="Question bubble"
                    width={370}
                    height={370}
                    className="w-86 h-86"
                  />
                  <div className="absolute top-[62%] left-1/2 -translate-x-1/2 -translate-y-1/2 text-center w-full px-2">
                    <p className="text-base font-small text-black leading-tight max-w-[200px] mx-auto italic">
                      Last time didn&apos;t go well
                      <span className="font-bold text-medium block mt-1">how can I start better now?</span>
                    </p>
                  </div>
                </div>
              </div>
              {/* Bottom Left Bubble */}
              <div className="absolute bottom-28 right-8">
                <div className="relative">
                  <Image
                    src="/assets/images/client/question_bubble_bottom_left.svg"
                    alt="Question bubble"
                    width={312}
                    height={323}
                    className="w-[324px] h-[324px] mx-auto"
                  />
                  <div className="absolute bottom-24 left-[45%] transform -translate-x-1/2 text-center">
                    <p className="text-base font-semibold text-white leading-tight max-w-[180px] mx-auto italic">
                      How to find the
                      <span className="font-bold text-lg block mt-1">Right therapist?</span>
                    </p>
                  </div>
                </div>
              </div>
              {/* Bottom Left Bubble */}
              <div className="absolute bottom-28 right-80">
                <div className="relative">
                  <Image
                    src="/assets/images/client/question_bubble_bottom_right.svg"
                    alt="Question bubble"
                    width={345}
                    height={351}
                    className="w-86 h-86"
                  />
                  <div className="absolute top-[60%] left-[51%] transform -translate-x-1/2 text-center w-full px-2">
                    <p className="text-sm font-semibold text-black leading-tight max-w-[180px] mx-auto italic">
                      How does
                      <span className="font-bold text-base block mt-1">therapy work?</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Desktop Bottom Curve */}
          <div className="absolute bottom-0 left-0 right-0 h-28 bg-white" style={{
            clipPath: "ellipse(50% 100% at 50% 100%)"
          }} />
        </div>
      </div>
    </section>
  );
} 