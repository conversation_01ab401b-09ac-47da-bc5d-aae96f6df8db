import { ImageResponse } from 'next/og';
import { NextRequest } from 'next/server';

export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Get parameters from URL
    const therapistName = searchParams.get('name') || 'Therapist';
    const therapistTitle = searchParams.get('title') || 'Licensed Therapist';
    const therapistLocation = searchParams.get('location') || 'Online';
    const profileImage = searchParams.get('image') || '';

    // Process and validate profile image URL with better fallback logic
    let validProfileImage = '';

    if (profileImage && profileImage.trim() !== '' && profileImage !== 'null' && profileImage !== 'undefined') {
      try {
        // Decode the URL in case it's encoded
        const decodedProfileImage = decodeURIComponent(profileImage);

        // If it's already a full URL, validate it
        if (decodedProfileImage.startsWith('http://') || decodedProfileImage.startsWith('https://')) {
          new URL(decodedProfileImage);
          validProfileImage = decodedProfileImage;
        } else if (decodedProfileImage.startsWith('/')) {
          // If it's a relative path, make it absolute
          const baseUrl = 'https://app.thoughtpudding.com';
          const fullUrl = `${baseUrl}${decodedProfileImage}`;
          new URL(fullUrl);
          validProfileImage = fullUrl;
        } else {
          console.warn('Profile image URL does not use http/https protocol and is not a relative path:', decodedProfileImage);
        }
      } catch (urlError) {
        console.warn('Invalid profile image URL:', profileImage, 'Error:', urlError);
        // Try with the original URL as fallback
        try {
          if (profileImage.startsWith('http://') || profileImage.startsWith('https://')) {
            new URL(profileImage);
            validProfileImage = profileImage;
          }
        } catch (fallbackError) {
          console.warn('Fallback URL also invalid:', fallbackError);
        }
      }
    }

    // If no valid profile image found, use static fallback
    if (!validProfileImage) {
      validProfileImage = 'https://app.thoughtpudding.com/assets/images/newHome/therapist-profile-logo.png';
    }

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#6D84FF',
            backgroundImage: 'linear-gradient(135deg, #6D84FF 0%, #718FFF 100%)',
            position: 'relative',
          }}
        >
          {/* Background Pattern */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: 'radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%)',
            }}
          />

          {/* Main Content Container */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '90%',
              maxWidth: '1000px',
              padding: '40px',
              backgroundColor: 'rgba(255,255,255,0.95)',
              borderRadius: '24px',
              boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
            }}
          >
            {/* Left Side - Therapist Info */}
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                flex: 1,
                paddingRight: '40px',
              }}
            >
              {/* Thought Pudding Logo/Brand */}
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '20px',
                }}
              >
                <div
                  style={{
                    width: '40px',
                    height: '40px',
                    backgroundColor: '#6D84FF',
                    borderRadius: '8px',
                    marginRight: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '20px',
                    fontWeight: 'bold',
                  }}
                >
                  TP
                </div>
                <span
                  style={{
                    fontSize: '24px',
                    fontWeight: '600',
                    color: '#6D84FF',
                  }}
                >
                  Thought Pudding
                </span>
              </div>

              {/* Main Heading */}
              <h1
                style={{
                  fontSize: '48px',
                  fontWeight: '700',
                  color: '#1a1a1a',
                  lineHeight: '1.1',
                  marginBottom: '16px',
                  margin: 0,
                }}
              >
                Book therapy with
              </h1>

              {/* Therapist Name */}
              <h2
                style={{
                  fontSize: '56px',
                  fontWeight: '800',
                  color: '#6D84FF',
                  lineHeight: '1.1',
                  marginBottom: '16px',
                  margin: 0,
                }}
              >
                {therapistName}
              </h2>

              {/* Therapist Details */}
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '8px',
                  marginBottom: '24px',
                }}
              >
                <span
                  style={{
                    fontSize: '28px',
                    color: '#4a5568',
                    fontWeight: '500',
                  }}
                >
                  {therapistTitle}
                </span>
                <span
                  style={{
                    fontSize: '24px',
                    color: '#718096',
                    fontWeight: '400',
                  }}
                >
                  📍 {therapistLocation}
                </span>
              </div>

              {/* Features */}
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '16px',
                  flexWrap: 'wrap',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: '#f0f9ff',
                    padding: '8px 16px',
                    borderRadius: '20px',
                    border: '2px solid #0ea5e9',
                  }}
                >
                  <span style={{ fontSize: '18px', color: '#0ea5e9', fontWeight: '600' }}>
                    🔒 Secure Platform
                  </span>
                </div>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: '#f0fdf4',
                    padding: '8px 16px',
                    borderRadius: '20px',
                    border: '2px solid #22c55e',
                  }}
                >
                  <span style={{ fontSize: '18px', color: '#22c55e', fontWeight: '600' }}>
                    💻 Online Sessions
                  </span>
                </div>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: '#fefce8',
                    padding: '8px 16px',
                    borderRadius: '20px',
                    border: '2px solid #eab308',
                  }}
                >
                  <span style={{ fontSize: '18px', color: '#eab308', fontWeight: '600' }}>
                    ⚡ Easy Booking
                  </span>
                </div>
              </div>
            </div>

            {/* Right Side - Profile Image (Always show an image) */}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '280px',
                height: '280px',
                borderRadius: '50%',
                overflow: 'hidden',
                border: '6px solid white',
                boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
                flexShrink: 0,
                backgroundColor: '#f0f0f0',
              }}
            >
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={validProfileImage}
                alt={therapistName}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                }}
                crossOrigin="anonymous"
                onError={(e) => {
                  // If image fails to load, show initials instead
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const parent = target.parentElement;
                  if (parent) {
                    parent.innerHTML = `<div style="display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; background-color: #e2e8f0; font-size: 72px; color: #6D84FF; font-weight: bold;">${therapistName.charAt(0).toUpperCase()}</div>`;
                  }
                }}
              />
            </div>
          </div>

          {/* Bottom Brand */}
          <div
            style={{
              position: 'absolute',
              bottom: '30px',
              right: '40px',
              display: 'flex',
              alignItems: 'center',
              color: 'rgba(255,255,255,0.9)',
              fontSize: '18px',
              fontWeight: '500',
            }}
          >
            Powered by Thought Pudding
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
        headers: {
          'Cache-Control': 'public, max-age=3600, must-revalidate',
          'Content-Type': 'image/png',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } catch (e: unknown) {
    const errorMessage = e instanceof Error ? e.message : 'Unknown error occurred';
    console.error('OG Image generation error:', errorMessage);
    console.error('Full error:', e);

    // Return a simple fallback image
    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#6D84FF',
            color: 'white',
            fontSize: '48px',
            fontWeight: 'bold',
          }}
        >
          <div>Thought Pudding</div>
          <div style={{ fontSize: '24px', marginTop: '20px' }}>
            Professional Therapy Platform
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
        headers: {
          'Cache-Control': 'public, max-age=3600, must-revalidate',
          'Content-Type': 'image/png',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  }
}
