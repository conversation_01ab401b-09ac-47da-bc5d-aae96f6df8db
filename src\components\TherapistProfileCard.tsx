"use client";

import Image from "next/image";
import Link from "next/link";

type TherapistProfileCardProps = {
  id: string;
  fullName: string;
  pronouns: string;
  designation: string;
  experience: string | number;
  profileImage: string;
  size?: "small" | "medium" | "large";
};

export default function TherapistProfileCard({
  id,
  fullName,
  pronouns,
  designation,
  experience,
  profileImage,
  size = "medium",
}: TherapistProfileCardProps) {
  // Size configurations
  const sizeConfig = {
    small: {
      container: "p-4 max-w-[200px]",
      image: "w-16 h-16",
      imageSize: "64px",
      nameText: "text-base",
      pronounsText: "text-xs",
      designationText: "text-xs",
      experienceText: "text-xs px-2 py-0.5",
      buttonText: "text-xs px-4 py-1",
    },
    medium: {
      container: "p-5 w-full max-w-[250px]",
      image: "w-20 h-20",
      imageSize: "80px",
      nameText: "text-lg",
      pronounsText: "text-xs",
      designationText: "text-xs",
      experienceText: "text-xs px-2 py-0.5",
      buttonText: "text-sm px-5 py-1.5",
    },
    large: {
      container: "p-6 max-w-[300px]",
      image: "w-24 h-24",
      imageSize: "96px",
      nameText: "text-xl",
      pronounsText: "text-sm",
      designationText: "text-sm",
      experienceText: "text-sm px-3 py-1",
      buttonText: "text-base px-6 py-2",
    },
  };

  const config = sizeConfig[size];

  return (
    <div className={`text-black ${config.container}`}>
      <div className="flex flex-col items-center">
        {/* Therapist Image */}
        <div className={`relative ${config.image} rounded-full overflow-hidden mb-3`}>
          <Image
            src={profileImage}
            alt={fullName}
            fill
            sizes={config.imageSize}
            className="object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "/assets/images/default-profile.png";
            }}
          />
        </div>

        {/* Therapist Info */}
        <div className="text-center">
          <h2 className={`${config.nameText} font-bold text-[#251D5C] gilmer-bold`}>
            {fullName}{" "}
            <span className={`${config.pronounsText} text-gray-600 font-normal gilmer-regular`}>
              ({pronouns})
            </span>
          </h2>
          <p className={`${config.designationText} text-gray-700 mt-1`}>{designation}</p>
          <div className="mt-2 mb-4">
            <span className={`${config.experienceText} bg-[#718FFF] text-white rounded-md inline-block`}>
              {experience}+ {parseInt(experience.toString()) === 1 ? "year" : "years"} of experience
            </span>
          </div>

          {/* View Profile Button */}
          <Link
            href={`/clients/${id}`}
            className={`${config.buttonText} bg-[#D9A052] text-white rounded-md hover:opacity-90 font-medium inline-block`}
          >
            View Profile
          </Link>
        </div>
      </div>
    </div>
  );
}
