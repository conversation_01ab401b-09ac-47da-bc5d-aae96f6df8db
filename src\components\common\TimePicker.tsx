import { Clock } from "@phosphor-icons/react";
import { useState, useEffect, useRef, ChangeEvent } from "react";

interface TimePickerProps {
  className?: string;
  value?: string;
  onChange?: (value: string) => void;
  fromDate?: Date | null; // Add fromDate prop
}

const TimePicker: React.FC<TimePickerProps> = ({
  className,
  value,
  onChange,
  fromDate,
}) => {
  const [selectedTime, setSelectedTime] = useState(value || "00:00"); // Default to "00:00"
  const [showDropdown, setShowDropdown] = useState(false);
  const [inputValue, setInputValue] = useState(value || "00:00");
  const [isValid, setIsValid] = useState(true);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Calculate current time for filtering if fromDate is today
  const isToday = fromDate
    ? fromDate.toDateString() === new Date().toDateString()
    : false;
  const currentTime = new Date();
  const currentHour = currentTime.getHours();
  const currentMinutes = currentTime.getMinutes();

  // Generate times with 10-minute intervals
  const times = Array.from({ length: 24 * 6 }, (_, i) => {
    const hour = Math.floor(i / 6);
    const minutes = (i % 6) * 10;
    const formattedHour = hour < 10 ? `0${hour}` : hour;
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
    return `${formattedHour}:${formattedMinutes}`;
  }).filter((time) => {
    if (isToday) {
      const [hour, minute] = time.split(":").map(Number);
      return (
        hour > currentHour || (hour === currentHour && minute > currentMinutes)
      );
    }
    return true;
  });

  useEffect(() => {
    if (value !== undefined) {
      setSelectedTime(value);
      setInputValue(value);
    }
  }, [value]);

  const handleSelectTime = (time: string) => {
    setSelectedTime(time);
    setInputValue(time);
    setShowDropdown(false);
    setIsValid(true);
    if (onChange) {
      // setSelectedTime(time);
      onChange(time);
    }
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const validateTime = (time: string): boolean => {
    const timeRegex = /^([0-1]\d|2[0-3]):([0-5]\d)$/;
    return timeRegex.test(time);
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    if (validateTime(newValue)) {
      setSelectedTime(newValue);
      setIsValid(true);
      if (onChange) {
        onChange(newValue);
      }
    } else {
      setIsValid(false);
    }
  };

  const handleInputFocus = () => {
    setShowDropdown(true);
  };

  return (
    <div className={`relative mt-2 ${className}`} ref={dropdownRef}>
      <div className="flex items-center justify-between">
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          placeholder="00:00"
          className={`w-full text-sm_18 py-3 px-2.5 border rounded-lg outline-none focus:border-green-600 ${
            isValid
              ? selectedTime === "00:00"
                ? "text-gray-400"
                : "text-primary border-[#D9D9D9]"
              : ""
          }`}
        />
        <button
          type="button"
          onClick={() => setShowDropdown(!showDropdown)}
          className="absolute right-2 top-1/2 transform -translate-y-1/2"
        >
          <Clock size={20} className="text-primary" />
        </button>
      </div>

      <div
        className={`absolute mt-1 w-full max-h-48 overflow-y-auto z-10 bg-white shadow-[0px_4px_12px_0px_#2C58BB1A] rounded-lg transition-all duration-200 ${
          showDropdown
            ? "visible opacity-100 translate-y-0"
            : "invisible opacity-0 -translate-y-5"
        }`}
      >
        {times.map((time) => (
          <div
            key={time}
            onClick={() => handleSelectTime(time)}
            className={`px-4 py-2 cursor-pointer hover:bg-gray-100/20 transition duration-200 text-sm_18 ${
              selectedTime === time
                ? "bg-green-600/10 text-green-600"
                : "text-primary"
            }`}
          >
            {time}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TimePicker;
