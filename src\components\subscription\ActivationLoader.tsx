import React from 'react';

const ActivationLoader: React.FC = () => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm z-[9999]">
      <div className="bg-white p-6 rounded-xl shadow-lg flex flex-col items-center" style={{ maxWidth: '280px' }}>
        <div className="relative">
          {/* Main spinning circle */}
          <div className="w-16 h-16 rounded-full animate-spin-slow border-3 border-solid border-primary border-t-transparent"></div>
          
          {/* Secondary spinning circle */}
          <div className="absolute inset-0 rounded-full animate-spin-reverse border-3 border-dashed border-primary/30"></div>
          
          {/* Pulsing core */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-8 h-8 rounded-full animate-pulse bg-primary/20"></div>
          </div>
          
          {/* Center dot */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-1.5 h-1.5 rounded-full bg-primary animate-ping"></div>
          </div>
        </div>
        
        {/* Text with typing animation */}
        <div className="mt-4 text-center">
          <p className="text-base font-medium text-primary">Activating your plan</p>
          <div className="flex items-center justify-center">
            <span className="animate-bounce mx-0.5 delay-100">.</span>
            <span className="animate-bounce mx-0.5 delay-200">.</span>
            <span className="animate-bounce mx-0.5 delay-300">.</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivationLoader;
