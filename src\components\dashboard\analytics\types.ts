export interface PaymentOverviewData {
  avgSessionFee: number;
  noOfSessions: number;
  totalPayment: number;
  receivedAfterSessions: {
    amount: number;
    percentage: number;
  };
  cancellationFees: {
    amount: number;
    percentage: number;
  };
  advancePayment: {
    amount: number;
    percentage: number;
  };
}

export interface BusinessDataItem {
  title: string;
  value: string;
  subtitle: string;
  color: string;
  textColor: string;
  imageWidth?: number;
  imageHeight?: number;
}

export interface ClientInsight {
  id: string;
  name: string;
  email: string;
  avatar: string;
  count: number;
  type: 'delays' | 'cancellation' | 'reschedules' | 'cancelled';
}

export interface AccordionSection {
  id: string;
  title: string;
  color: string;
  isExpanded: boolean;
  clients?: ClientInsight[];
  insights?: {
    type: 'regular' | 'delayed' | 'cancellation';
    client: {
      name: string;
      email: string;
      avatar: string;
    };
    description: string;
    badge?: string;
  }[];
}
