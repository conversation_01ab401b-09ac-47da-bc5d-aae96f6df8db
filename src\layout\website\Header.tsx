"use client";

import Link from "next/link";
import Image from "next/image";
import { CgProfile } from "react-icons/cg";
import { useEffect, useState, Fragment } from "react";
import { useSearchParams, usePathname } from "next/navigation";
import Login from "@/components/common/Login";

const Header: React.FC = () => {
  const queryParams = useSearchParams();
  const pathname = usePathname();
  const [isScrolled, setIsScrolled] = useState(false);
  const [loginOpen, setLoginOpen] = useState(false);
  const [th_id, setTherapistId] = useState<string | null>(null);
  const [userStep, setUserStep] = useState<string | null>(null);

  const therapist_id = queryParams.get("q");
  const user_step = queryParams.get("step");
  const loginParam = queryParams.get("login");

  useEffect(() => {
    if (therapist_id && user_step) {
      setTherapistId(therapist_id);
      setUserStep(user_step);
      setLoginOpen(true);
    }
  }, [therapist_id, user_step]);

  useEffect(() => {
    if (loginParam) {
      setLoginOpen(true);
    }

    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [loginParam]);

  return (
    <Fragment>
      <header
        className={`fixed top-0 z-50 transition-all duration-300
        w-full mx-0
        ${isScrolled ? "backdrop-blur-md shadow-md" : ""}`}
      >
        <div className="flex items-center justify-between px-4 py-3">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <Image
              src="/assets/images/newHome/Home-logo.png"
              alt="Thought Pudding Logo"
              width={275}
              height={104}
              className="object-contain w-[109px] h-[42px] md:w-[192px] md:h-[72px] lg:w-[275px] lg:h-[104px]"
              priority
            />
          </Link>

    
          {/* Navigation - Mobile */}
          <div className="md:hidden flex items-center gap-2">
            <select
              className="text-xs bg-transparent border border-gray-300 rounded px-2 py-1"
              value={pathname}
              onChange={(e) => window.location.href = e.target.value}
            >
              <option value="/therapist">For Therapists</option>
              <option value="/clients">For Clients</option>
            </select>
          </div>

          {/* Login Button */}
          <button
            onClick={() => setLoginOpen(true)}
            className="flex items-center gap-2 px-3 py-1.5 md:px-4 md:py-2 bg-[#251D5C] text-white rounded-lg transition hover:opacity-90"
          >
            <span className="text-xs md:text-sm lg:text-base">Login</span>
            <CgProfile className="text-base md:text-lg lg:text-xl" />
          </button>
        </div>
      </header>

      {/* Login Modal */}
      <Login
        setLoginOpen={setLoginOpen}
        loginOpen={loginOpen}
        th_id={th_id}
        userStep={userStep}
      />
    </Fragment>
  );
};

export default Header;
