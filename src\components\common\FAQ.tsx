import Link from "next/link";
import { useEffect, useState } from "react";
import { PiCaretDownBold } from "react-icons/pi";

const FAQData = [
  {
    title: "Who can use this platform?",
    content:
      "Our platform is built to support all therapists in private practice, whether youʼre a counseling psychologist, clinical psychologist, or psychotherapist. Whether youʼre just starting, a part time practitioner to those of you managing a full client load, weʼre here for every stage of your journey. ",
  },
  {
    title: "What is the pricing?",
    content:
      "It is absolutely free to use till December. So hurry up, schedule a demo with us now! ",
  },
  {
    title: "How do you ensure confidentiality of data?",
    content:
      "Our platform is for therapistʼs eyes only. Any and all client data that is used in your private practice is protected with utmost seriousness. You can view our detailed policy here (link to privacy policy). ",
  },

  {
    title: "What is your verification process?",
    content:
      "We verify every therapist individually and go through their degree, qualification and license if required before approving access to the platform. In case you have a collective or clinic, the same process applies. ",
  },
  {
    title: "How do you keep client data confidential?",
    content: (
      <span>
        We securely store client data using encryption and limit access to
        authorized personnel only. For more details, please see our{" "}
        <Link
          href="/privacy-policy"
          className="text-yellow-600 font-medium underline"
        >
          Privacy Policy.
        </Link>
      </span>
    ),
  },
];

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleAccordion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      const faqElements = document.querySelectorAll(".faq-item");
      if (
        !Array.from(faqElements).some((el) => el.contains(event.target as Node))
      ) {
        setOpenIndex(null);
      }
    }
    window.addEventListener("click", handleClickOutside);
    return () => {
      window.removeEventListener("click", handleClickOutside);
    };
  }, [openIndex]);

  return (
    <div className="container mx-auto px-4 xl:px-0 sm:pt-120px pt-50px">
      <h2 className="sm:text-38px_45 text-xl/7 font-semibold text-primary text-center sm:pb-50px pb-2.5">
        Find answers to your questions here
      </h2>
      {FAQData.map((item, index) => (
        <div key={index} className="faq-item">
          <div className="sm:py-30px sm:px-5 py-5 border-b overflow-hidden">
            <div
              className="flex gap-4 items-center justify-between bg-white cursor-pointer"
              onClick={() => toggleAccordion(index)}
            >
              <p
                className={`sm:text-xl/6 text-sm/4 transition-all duration-300 ${
                  openIndex === index
                    ? "text-green-600 font-semibold"
                    : "text-primary font-medium"
                }`}
              >
                {item.title}
              </p>
              <PiCaretDownBold
                className={`sm:w-6 sm:h-6 w-3.5 h-3.5 transition-transform duration-300 ${
                  openIndex === index ? "rotate-180" : "rotate-0"
                }`}
              />
            </div>
            <div
              className={`max-w-[90%] overflow-hidden transition-max-height ease-in-out duration-500 ${
                openIndex === index ? "max-h-96" : "max-h-0"
              }`}
            >
              <div className="bg-white">
                <p className="sm:text-lg/7 text-sm/5 text-primary/75  sm:pt-4 pt-2">
                  {item.content}
                </p>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default FAQ;
