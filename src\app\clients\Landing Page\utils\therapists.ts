import { TherapistData, TherapistFilters, TherapistSearchResult } from '../types/therapist';
import therapistsData from '../data/therapists.json';

/**
 * Get all therapists from the data source
 */
export function getAllTherapists(): TherapistData[] {
  return therapistsData.therapists as TherapistData[];
}

/**
 * Get a specific therapist by ID
 */
export function getTherapistById(id: string): TherapistData | null {
  const therapists = getAllTherapists();
  return therapists.find(therapist => therapist.id === id) || null;
}

/**
 * Get a specific therapist by slug (for URL routing)
 */
export function getTherapistBySlug(slug: string): TherapistData | null {
  const therapists = getAllTherapists();
  return therapists.find(therapist => therapist.slug === slug) || null;
}

/**
 * Search and filter therapists based on criteria
 */
export function searchTherapists(filters: TherapistFilters): TherapistSearchResult {
  let therapists = getAllTherapists();

  // Filter by therapy types
  if (filters.therapyTypes && filters.therapyTypes.length > 0) {
    therapists = therapists.filter(therapist =>
      filters.therapyTypes!.some(type =>
        therapist.therapyTypes.some(tType =>
          tType.toLowerCase().includes(type.toLowerCase())
        )
      )
    );
  }

  // Filter by concerns
  if (filters.concerns && filters.concerns.length > 0) {
    therapists = therapists.filter(therapist =>
      filters.concerns!.some(concern =>
        therapist.concerns.some(tConcern =>
          tConcern.toLowerCase().includes(concern.toLowerCase())
        )
      )
    );
  }

  // Filter by languages
  if (filters.languages && filters.languages.length > 0) {
    therapists = therapists.filter(therapist =>
      filters.languages!.some(language =>
        therapist.languages.some(tLanguage =>
          tLanguage.toLowerCase().includes(language.toLowerCase())
        )
      )
    );
  }

  // Filter by session format
  if (filters.sessionFormat) {
    therapists = therapists.filter(therapist =>
      therapist.sessionFormat === filters.sessionFormat ||
      therapist.sessionFormat === 'both'
    );
  }

  // Filter by price range
  if (filters.priceRange) {
    therapists = therapists.filter(therapist => {
      const amount = therapist.fees.amount;
      if (typeof amount === 'number') {
        return amount >= filters.priceRange!.min && amount <= filters.priceRange!.max;
      } else if (typeof amount === 'string') {
        // Handle range strings like "2500-4000"
        const [min, max] = amount.split('-').map(Number);
        return (min >= filters.priceRange!.min && min <= filters.priceRange!.max) ||
               (max >= filters.priceRange!.min && max <= filters.priceRange!.max) ||
               (min <= filters.priceRange!.min && max >= filters.priceRange!.max);
      }
      return false;
    });
  }

  return {
    therapists,
    totalCount: therapists.length,
    filters
  };
}

/**
 * Get featured therapists (for homepage carousel)
 */
export function getFeaturedTherapists(): TherapistData[] {
  const therapists = getAllTherapists();
  return therapists.filter(therapist => therapist.featured);
}

/**
 * Get therapists for carousel (with rotation for display)
 */
export function getCarouselTherapists(): TherapistData[] {
  return getAllTherapists();
}

/**
 * Get related therapists (exclude current therapist)
 */
export function getRelatedTherapists(currentTherapistId: string, limit: number = 3): TherapistData[] {
  const therapists = getAllTherapists();
  return therapists
    .filter(therapist => therapist.id !== currentTherapistId)
    .slice(0, limit);
}

/**
 * Get unique values for filters
 */
export function getFilterOptions() {
  const therapists = getAllTherapists();
  
  const therapyTypes = Array.from(
    new Set(therapists.flatMap(t => t.therapyTypes))
  ).sort();
  
  const concerns = Array.from(
    new Set(therapists.flatMap(t => t.concerns))
  ).sort();
  
  const languages = Array.from(
    new Set(therapists.flatMap(t => t.languages))
  ).sort();
  
  const sessionFormats = Array.from(
    new Set(therapists.map(t => t.sessionFormat))
  ).filter(format => format !== 'both') as ('online' | 'in-person')[];
  
  const priceRange = {
    min: Math.min(...therapists.map(t => {
      const amount = t.fees.amount;
      if (typeof amount === 'number') {
        return amount;
      } else if (typeof amount === 'string') {
        return Number(amount.split('-')[0]);
      }
      return 0;
    })),
    max: Math.max(...therapists.map(t => {
      const amount = t.fees.amount;
      if (typeof amount === 'number') {
        return amount;
      } else if (typeof amount === 'string') {
        return Number(amount.split('-')[1]);
      }
      return 0;
    }))
  };

  return {
    therapyTypes,
    concerns,
    languages,
    sessionFormats,
    priceRange
  };
}

/**
 * Generate therapist URL slug
 */
export function generateTherapistUrl(therapist: TherapistData): string {
  return `/therapists/${therapist.slug}`;
}

/**
 * Validate therapist data structure
 */
export function validateTherapistData(therapist: TherapistData): therapist is TherapistData {
  return (
    typeof therapist.id === 'string' &&
    typeof therapist.slug === 'string' &&
    typeof therapist.name === 'string' &&
    typeof therapist.title === 'string' &&
    typeof therapist.image === 'string' &&
    Array.isArray(therapist.languages) &&
    Array.isArray(therapist.therapyTypes) &&
    Array.isArray(therapist.concerns) &&
    typeof therapist.fees === 'object' &&
    (typeof therapist.fees.amount === 'number' || typeof therapist.fees.amount === 'string')
  );
} 