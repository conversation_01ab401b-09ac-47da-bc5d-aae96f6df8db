import { NextRequest, NextResponse } from 'next/server';
import sharp from 'sharp';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;

    const imageUrl = searchParams.get('url');
    const maxSize = parseInt(searchParams.get('maxSize') || '300000'); // Default 300KB

    if (!imageUrl) {
      return NextResponse.json({ error: 'Image URL is required' }, { status: 400 });
    }

    // Fetch the original image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status}`);
    }

    const imageBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(imageBuffer);

    // Get image metadata
    const metadata = await sharp(buffer).metadata();
    
    // Start with high quality and reduce if needed
    let quality = 85;
    let compressedBuffer: Buffer;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      attempts++;
      
      // Create sharp instance with compression
      let sharpInstance = sharp(buffer);

      // Resize if image is too large (maintain aspect ratio)
      if (metadata.width && metadata.width > 1200) {
        sharpInstance = sharpInstance.resize(1200, null, {
          withoutEnlargement: true,
          fit: 'inside'
        });
      }

      // Apply format-specific compression
      if (metadata.format === 'png') {
        sharpInstance = sharpInstance.png({
          quality: quality,
          compressionLevel: 9,
          adaptiveFiltering: true,
          force: true
        });
      } else {
        // Default to JPEG for better compression
        sharpInstance = sharpInstance.jpeg({
          quality: quality,
          progressive: true,
          mozjpeg: true,
          force: true
        });
      }

      compressedBuffer = await sharpInstance.toBuffer();

      // If still too large, reduce quality
      if (compressedBuffer.length > maxSize && quality > 20) {
        quality -= 10;
      } else {
        break;
      }
    } while (compressedBuffer.length > maxSize && attempts < maxAttempts);

    // If still too large after all attempts, try aggressive compression
    if (compressedBuffer.length > maxSize) {
      compressedBuffer = await sharp(buffer)
        .resize(800, null, { withoutEnlargement: true, fit: 'inside' })
        .jpeg({ quality: 20, progressive: true, mozjpeg: true, force: true })
        .toBuffer();
    }

    // Determine content type
    const contentType = metadata.format === 'png' && compressedBuffer.length <= maxSize
      ? 'image/png'
      : 'image/jpeg';

    // Return compressed image with appropriate headers
    return new NextResponse(compressedBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Length': compressedBuffer.length.toString(),
        'Cache-Control': 'public, max-age=86400, s-maxage=86400', // Cache for 24 hours
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('Image compression error:', error);
    
    // Return a fallback response
    return NextResponse.json(
      { 
        error: 'Failed to compress image',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
