"use client";
import Button from "@/components/common/Button";
import DatePicker from "@/components/common/DatePicker";
import TablePagination from "@/components/common/TablePagination";
import Tabs from "@/components/common/Tabs";
import ActivityCard from "@/components/dashboard/common/ActivityCard";
import PatientTBody from "@/components/dashboard/common/table/PatientTBody";
import THeader from "@/components/dashboard/common/table/THeader";
import CommonModal from "@/components/dashboard/CommonModal";
import DaysSelectDropdown from "@/components/dashboard/DaysSelectDropdown";
import EditClientSidebar from "@/components/dashboard/EditClientSidebar";
import DashboardLayout from "@/layout/dashboard/DashboardLayout";
import {
  FilterParams,
  useGetClientCount,
  useGetClients,
} from "@/services/clients.service";
import { FunnelSimple, MagnifyingGlass, X } from "@phosphor-icons/react";
import moment from "moment";
import { useEffect, useState } from "react";

const clientsTabs = [
  { label: "Public Calendar ", value: "public-calendar" },
  { label: "Active", value: "true" },
  { label: "Inactive", value: "false" },
  { label: "All", value: "" },
];

const sessionTableHeader = [
  "Name",
  "Contact Number",
  "Completed session",
  "status",
  "Appointment",
  "actions",
];

const Patient = () => {
  const [isMonthsDropSelect, setIsMonthsDropSelect] = useState("Today");
  const [isFilter, setIsFilter] = useState(false);
  const [isConfirmation, setIsConfirmation] = useState(false);
  const [isEditClient, setIsEditClient] = useState(false);
  const [singleClientById, setSingleClientById] = useState("");
  const [activeTable, setActiveTable] = useState(clientsTabs[0]);
  const [startDate, setStartDate] = useState(() => {
    const start = new Date();
    start.setHours(0, 0, 0, 0); // Set to start of the day (00:00:00)
    return start;
  });

  const [endDate, setEndDate] = useState(() => {
    const end = new Date();
    end.setHours(23, 59, 59, 999); // Set to end of the day (23:59:59)
    return end;
  });

  const [isFilterApplied, setIsFilterApplied] = useState(false);
  const [inactiveClient, setInactiveClient] = useState(false);

  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");

  const [searchStartDate, setSearchStartDate] = useState<string | null>(null);
  const [searchEndDate, setSearchEndDate] = useState<string | null>(null);

  const [filterparams, setFilterparams] = useState<FilterParams>({});
  const [currentPage, setCurrentPage] = useState(1);

  const pageSize = 5; // Set the page size as required

  // For public calendar tab, don't send isActive filter, only send fromPublicCalender filter
  const isActiveValue = activeTable.value === "public-calendar" ? "" : activeTable.value;

  const query =
    `pageSize=${pageSize}&pageNumber=${currentPage}&isActive=${isActiveValue}&searchText=${debouncedSearchText}` +
    (filterparams && filterparams.searchStartDate && filterparams.searchEndDate
      ? `&startDate=${filterparams.searchStartDate}&endDate=${filterparams.searchEndDate}`
      : "");

  const { clientsData, clientsLoading, clientsCount } = useGetClients(
    pageSize,
    currentPage,
    isActiveValue,
    debouncedSearchText,
    filterparams
  );

  const totalPages = Math.ceil(clientsCount / pageSize);

  // Reset page to 1 when the active tab changes
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTable, isFilterApplied]);

  // Handle public calendar filter when tab is selected
  useEffect(() => {
    if (activeTable.value === "public-calendar") {
      setFilterparams(prev => ({
        ...prev,
        fromPublicCalender: true
      }));
      setIsFilterApplied(true);
    } else {
      // Remove public calendar filter when switching to other tabs
      setFilterparams(prev => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { fromPublicCalender, ...rest } = prev;
        // Only set isFilterApplied to false if no other filters are applied
        if (!rest.searchStartDate && !rest.searchEndDate) {
          setIsFilterApplied(false);
        }
        return rest;
      });
    }
  }, [activeTable.value]);

  // activity section start
  const { clientsCountData } = useGetClientCount(startDate, endDate) as {
    clientsCountData: { new?: number; active?: number; in_active?: number };
  };

  const activity = [
    {
      title: "New Clients",
      count: `${clientsCountData?.new || 0}`,
      percentage: "",
    },
    {
      title: "Active Clients",
      count: `${clientsCountData?.active || 0}`,
      percentage: "",
    },
    {
      title: "In-Active Clients",
      count: `${clientsCountData?.in_active || 0}`,
      percentage: "",
    },
  ];

  const handleApplySearchFilter = () => {
    // Set filterParams to valuen trigger a new API call
    setFilterparams({
      searchStartDate: searchStartDate
        ? moment(searchStartDate).startOf("day").toISOString()
        : "",
      searchEndDate: searchEndDate
        ? moment(searchEndDate).endOf("day").toISOString()
        : "",
    });

    // show reset filter
    setIsFilterApplied(true);

    // Close the modal after applying the filter
    setIsFilter(false);
  };

  // resetFilter
  const resetFilter = () => {
    // Reset search-related states
    setSearchStartDate(null);
    setSearchEndDate(null);
    setFilterparams({});
    setIsFilterApplied(false); // Hide Reset button
  };

  // set search value
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchText(searchText);
      setCurrentPage(1);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchText]);

  return (
    <DashboardLayout>
      <div className="bg-white mt-5 rounded-base overflow-hidden">
        {/* activity section */}
        <div className="p-5">
          <div className="flex items-center justify-between">
            <h2 className="text-lg/5 sm:text-xl/6 text-primary font-semibold">
              Activities
            </h2>
            <DaysSelectDropdown
              value={isMonthsDropSelect}
              onChange={(value: unknown) =>
                setIsMonthsDropSelect(value as string)
              }
              DropClass=""
              setStartDate={setStartDate}
              setEndDate={setEndDate}
            />
          </div>
          <div className="pt-5 grid sm:grid-cols-3 gap-5">
            {activity?.map((items, index) => (
              <ActivityCard
                key={index}
                title={items.title}
                count={items.count}
                percentage={items.percentage}
                borderColor={
                  index === 0
                    ? "border-[#48A400]"
                    : index === 1
                    ? "border-[#1339FF]"
                    : index === 2
                    ? "border-[#FF5C00]"
                    : "border-[#D813FF]"
                }
              />
            ))}
          </div>
        </div>

        {/* table */}
        <div className="p-5">
          <div className="flex flex-wrap gap-5 items-center justify-between">
            <Tabs
              tabs={clientsTabs}
              activeTab={activeTable.label}
              setActiveTab={(tab) => setActiveTable(tab)}
              sessionCount={clientsCount}
            />
            <div className="flex items-center gap-2 max-w-[391px] sm:min-w-[391px] w-full sm:py-15px sm:px-5 p-3 border border-[#9B9DB7] rounded-full text-xs text-primary">
              <MagnifyingGlass className="text-primary/50 min-w-5 w-5 h-5" />
              <input
                type="search"
                placeholder="Search your client name and id"
                className="outline-none w-full placeholder:text-primary/50"
                onChange={(e) => {
                  setSearchText(e.target.value);
                }}
              />
              <span className="text-primary/50">|</span>
              <div className="flex items-center bg-green-600/5 py-1 px-2.5 rounded-full gap-3">
                <FunnelSimple
                  size={20}
                  className="text-green-600 cursor-pointer"
                  onClick={() => {
                    setIsFilter(!isFilter);
                  }}
                />
                {isFilterApplied && (
                  <div
                    onClick={resetFilter}
                    className="w-5 h-5 rounded-full border border-primary/20 text-primary flex items-center justify-center bg-white cursor-pointer"
                  >
                    <X size={12} />
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="pt-10">
            <div className="w-full border border-green-600/25 rounded-base overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full  bg-white">
                  <THeader data={sessionTableHeader} />
                  <PatientTBody
                    TableData={clientsData}
                    clientsLoading={clientsLoading}
                    setIsEditClient={setIsEditClient}
                    isEditClient={isEditClient}
                    setSingleClientById={setSingleClientById}
                  />
                </table>
              </div>
              <TablePagination
                totalPages={totalPages}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            </div>
          </div>
        </div>
      </div>

      <EditClientSidebar
        isEditClient={isEditClient}
        setIsEditClient={setIsEditClient}
        singleClientById={singleClientById}
        query={query}
        setIsConfirmation={setIsConfirmation}
        inactiveClient={inactiveClient}
        setInactiveClient={setInactiveClient}
        // singleClientsData={singleClientsData}
      />
      {/* Filter Modal */}
      <CommonModal title="Filter" isClose={isFilter} setIsClose={setIsFilter}>
        <div className="py-7.5 space-y-5">
          <div>
            <label className="text-base/5 text-primary font-medium">
              Start Date
            </label>
            <DatePicker
              value={searchStartDate ?? ""}
              placeholder={`DD/MM/YYYY`}
              className={`!mt-3`}
              onChange={(date) => {
                setSearchStartDate(date);
              }}
            />
          </div>
          <div>
            <label className="text-base/5 text-primary font-medium">
              End Date
            </label>
            <DatePicker
              value={searchEndDate ?? ""}
              minDate={searchStartDate ?? ""}
              placeholder={`DD/MM/YYYY`}
              className={`!mt-3`}
              onChange={(date) => {
                setSearchEndDate(date);
              }}
            />
          </div>
        </div>
        <div className="flex items-center justify-end gap-3.5">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() => {
              setIsFilter(false);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px]`}
            disabled={!searchStartDate || !searchEndDate}
            onClick={handleApplySearchFilter}
          >
            Apply
          </Button>
        </div>
      </CommonModal>

      <CommonModal
        title="Inactive Client"
        isClose={isConfirmation}
        setIsClose={setIsConfirmation}
      >
        <p className="text-gray-500 text-sm/5 pt-5 max-w-[465px]">
        Making the user inactive will result in the removal of all future sessions.Are you sure you want to mark this client Inactive?
        </p>
        <div className="flex items-center justify-end gap-3.5 pt-7">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() => {
              setIsConfirmation(false);
              setInactiveClient(false);
            }}
            disabled={inactiveClient}
          >
            No
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px]`}
            onClick={() => setInactiveClient(true)}
            disabled={inactiveClient}
          >
            Yes
          </Button>
        </div>
      </CommonModal>
    </DashboardLayout>
  );
};

export default Patient;
