"use client";
import { X } from "@phosphor-icons/react";
import React, { useEffect, useState } from "react";
import Input from "../common/Input";
import SelectDropdown from "../common/SelectDropdown";
import Button from "../common/Button";

import { useFormik } from "formik";
import * as Yup from "yup";
import {
  createSchedule,
  getClientDetails,
  useGetFreeSlots,
} from "@/services/dashboard.service";
import { fetcher, formatDate, formatTime } from "@/utils/axios";
import { mutate } from "swr";
import endpoints from "@/utils/endpoints";
import { extractTenDigitMobile } from "@/utils/phoneUtils";

const genderOption = [
  "Cisgender Male",
  "Cisgender Female",
  "Transgender",
  "Non-Binary",
  "Prefer not to say",
];

const reminderOption = ["10-Minutes", "20-Minutes", "30-Minutes"];

interface FreeSlot {
  fromDate: string;
  toDate: string;
}

const FreeSlotsSidebar: React.FC<{
  freeSlot: boolean;
  setFreeSlot: (value: boolean) => void;
}> = ({ freeSlot, setFreeSlot }) => {
  const [activeSlotIndex, setActiveSlotIndex] = useState<number | null>(null);
  const [loading, setLoading] = useState(false); // Add loading state
  const { freeSlotData, freeSlotLoading } = useGetFreeSlots(freeSlot);

  // Formik setup
  const formik = useFormik({
    initialValues: {
      name: "",
      phone: "",
      emails: [],
      age: "",
      gender: "",
      recommendedWeekSelection: "", // Add recommendedWeek selection
      amount: "",
      reminder: "",
      summary: "",
    },
    validationSchema: Yup.object({
      name: Yup.string().required("Full name is required"),
      phone: Yup.string()
        .matches(/^[0-9]+$/, "Only digits are allowed")
        .length(10, "Mobile number must be exactly 10 digits"),
      // recurrence: Yup.string().required("Frequency is required"),
      emails: Yup.array()
        .of(Yup.string().email("Invalid email address"))
        .min(1, "Email is required"),
      age: Yup.number().positive("Age must be positive"),
      recommendedWeekSelection: Yup.string().required(
        "Please select a recommended time"
      ),
      amount: Yup.number()
        .positive("Payment must be a positive number")
        .required("Payment is required"),
      reminder: Yup.string().required("Reminder is required"),
      summary: Yup.string().required("Description is required"),
    }),
    onSubmit: async (values, { resetForm }) => {
      setLoading(true);
      try {
        const [startUtc, endUtc] = values.recommendedWeekSelection.split(" - ");

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { recommendedWeekSelection: _recommendedWeekSelection, reminder: _reminder, ...restValues } = values;

        // Extract 10-digit mobile number for backend
        const tenDigitPhone = extractTenDigitMobile(values.phone);

        const formData = {
          ...restValues,
          phone: tenDigitPhone || values.phone, // Use 10-digit phone or original if extraction fails
          fromDate: startUtc,
          toDate: endUtc,
          sessionDate: startUtc,
          recurrence: "Does not repeat",
          clientCountry: "India",
          description: "new session",
          location: "online",
          isBefore: false,
        };

        // Add your form submit logic here
        const data = await createSchedule(formData);

        // Reset the form, including startTime and endTime
        if (data.success) {
          const url = `${endpoints.dashboard.getFreeSlot}`;
          mutate(url, async () => {
            await fetcher(url);
          });
          setFreeSlot(false); // Close sidebar on save
          setActiveSlotIndex(null);
          resetForm();
        }
      } catch (error) {
        console.error("Error submitting form:", error);
      } finally {
        setLoading(false);
      }
    },
  });

  const handleEmailBlur = async (email: string) => {
    console.log("function call");
    // Check if email is valid
    if (formik.errors.emails) {
      return; // If there are errors, exit the function
    }

    // Call the useGetClientDetails API function
    else {
      try {
        const clientDetails = await getClientDetails(email);

        console.log(clientDetails, "clientDetails...........");
        // setClientDetails(clientDetails);

        if (clientDetails) {
          if (clientDetails.phone) {
            formik.setFieldValue("phone", clientDetails.phone);
          }
          if (clientDetails.age) {
            formik.setFieldValue("age", clientDetails.age);
          }
          if (clientDetails.gender) {
            formik.setFieldValue("gender", clientDetails.gender);
          }
          if (clientDetails.defaultSessionAmount) {
            await formik.setFieldValue(
              "amount",
              Number(clientDetails.defaultSessionAmount)
            );
          }
          if (clientDetails.name) {
            await formik.setFieldValue("name", clientDetails.name);
          }
          setTimeout(async () => {
            await formik.validateField("amount");
            await formik.validateField("name");
          }, 0);
        }
      } catch (error) {
        // Handle error in API call
        console.error("Error fetching client details:", error);
      }
    }
  };

  useEffect(() => {
    const handleBodyScroll = (shouldLock: boolean) => {
      if (shouldLock) {
        document.body.style.overflow = "hidden";
      } else {
        document.body.style.overflow = "";
      }
    };

    handleBodyScroll(freeSlot);

    return () => handleBodyScroll(false);
  }, [freeSlot]);

  return (
    <div
      className={`fixed w-full h-full bg-black/20 top-0 left-0 z-[999] ${
        freeSlot ? "visible" : "invisible"
      }`}
      onClick={() => {
        setFreeSlot(false);
        formik.resetForm();
      }}
    >
      <div
        className={`max-w-[416px] w-full  bg-white absolute top-0 right-0 h-full transition-all duration-300 ${
          freeSlot ? "translate-x-0" : "translate-x-full"
        }`}
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside the sidebar
      >
        <div className="relative flex flex-col h-[100svh] sm:h-screen overflow-y-auto">
          {/* sidebar header */}
          <div className="px-5 py-3.5 shadow-[0px_4px_12px_0px_#0000000F] flex justify-between items-center sticky top-0">
            <h3 className="text-lg font-medium text-[#242424]">Free Slots</h3>
            <button
              onClick={() => {
                setFreeSlot(false);
                formik.resetForm();
              }}
            >
              <X size={20} />
            </button>
          </div>

          {/* Loader overlay */}
          {loading && (
            <div className="absolute inset-0 flex justify-center items-center bg-black/50 z-[1000]">
              <div className="spinner-border animate-spin h-12 w-12 border-4 border-t-transparent border-white rounded-full"></div>
            </div>
          )}
          {/* content */}
          <div className="p-5 flex-1 overflow-auto">
            <div>
              <h4 className="text-sm/5 font-medium text-[#242424]">
                Recommended Current Week <span className="text-red-600">*</span>
              </h4>
              {freeSlotLoading ? (
                <div className="grid grid-cols-2 gap-5 pt-[15px]">
                  {Array.from({ length: 4 }).map((_, index) => (
                    <div
                      key={index}
                      className="p-3 rounded border border-gray-200 bg-gray-100 animate-pulse"
                    >
                      {/* Skeleton Title */}
                      <div className="h-4 bg-gray-200 rounded w-3/5 mb-2"></div>
                      {/* Skeleton Subtitle */}
                      <div className="h-3 bg-gray-200 rounded w-4/5"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-5 pt-[15px]">
                  {freeSlotData?.map((item: FreeSlot, index: number) => (
                    <button
                      key={index}
                      onClick={() => {
                        formik.setFieldValue(
                          "recommendedWeekSelection",
                          `${item.fromDate} - ${item.toDate}`
                        );
                        setActiveSlotIndex(index); // Set the clicked slot as active
                      }}
                      className={`text-center p-3 rounded text-sm/5 border border-[#D9D9D9]
                      ${
                        activeSlotIndex === index
                          ? "bg-green-600/10"
                          : "hover:bg-green-600/5"
                      }
                      focus:border-green-600`}
                    >
                      <p className="font-medium text-primary">
                        {formatDate(item.fromDate)}
                      </p>
                      <p className="text-gray-500 uppercase">
                        {formatTime(item.fromDate) +
                          " - " +
                          formatTime(item.toDate)}
                      </p>
                    </button>
                  ))}
                  {formik.touched.recommendedWeekSelection &&
                  formik.errors.recommendedWeekSelection ? (
                    <div className="text-red-500 text-sm mt-1 col-span-2">
                      {formik.errors.recommendedWeekSelection}
                    </div>
                  ) : null}
                </div>
              )}
            </div>

            <hr className="border-divider my-5" />
            <div>
              <h4 className="text-sm/5 font-medium text-[#5E585A]">
                Clients details
              </h4>
              <form
                onSubmit={formik.handleSubmit}
                className="grid grid-cols-2 gap-5 mt-[15px]"
              >
                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Full Name <span className="text-red-600">*</span>
                  </label>
                  <Input
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    name="name"
                    type={"text"}
                    placeholder={"Enter Full Name"}
                  />
                  {formik.touched.name && formik.errors.name && (
                    <div className="text-red-500 text-sm mt-1">
                      {formik.errors.name}
                    </div>
                  )}
                </div>

                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Email <span className="text-red-600">*</span>
                  </label>
                  <Input
                    type={"emails"}
                    placeholder={"Enter Email"}
                    value={formik.values.emails.join(", ")}
                    onChange={(e) =>
                      formik.setFieldValue(
                        "emails",
                        e.target.value.split(",").map((email) => email.trim())
                      )
                    }
                    name="emails"
                    onBlur={() => handleEmailBlur(formik.values.emails[0])} // Call handleEmailBlur on blur
                    // icon={`email`}
                  />
                  {formik.touched.emails && formik.errors.emails && (
                    <div className="text-red-500 text-sm mt-1">
                      {formik.errors.emails}
                    </div>
                  )}
                </div>

                <div>
                  <label className="text-sm/5 text-primary font-medium">
                    Amount <span className="text-red-600">*</span>
                  </label>
                  <Input
                    name="amount"
                    value={formik.values.amount}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter amount"
                    type="number"
                    // icon={`rup`}
                  />
                  {formik.touched.amount && formik.errors.amount ? (
                    <div className="text-red-500 text-sm">
                      {formik.errors.amount}
                    </div>
                  ) : null}
                </div>
                <div>
                  <label className="text-sm/5 text-primary font-medium">
                    Reminder <span className="text-red-600">*</span>
                  </label>
                  <SelectDropdown
                    options={reminderOption}
                    value={formik.values.reminder}
                    onChange={(val) => formik.setFieldValue("reminder", val)}
                    placeholder="Select ..."
                  />
                  {formik.touched.reminder && formik.errors.reminder ? (
                    <div className="text-red-500 text-sm">
                      {formik.errors.reminder}
                    </div>
                  ) : null}
                </div>

                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Mobile Number
                  </label>
                  <Input
                    value={formik.values.phone}
                    onChange={formik.handleChange}
                    name="phone"
                    type="tel"
                    placeholder={"Enter Number"}
                    // icon={`number`}
                  />
                  {formik.touched.phone && formik.errors.phone && (
                    <div className="text-red-500 text-sm mt-1">
                      {formik.errors.phone}
                    </div>
                  )}
                </div>

                <div>
                  <label className="text-sm/5 text-primary font-medium">
                    Age
                  </label>
                  <Input
                    type={"text"}
                    placeholder={"Enter Age"}
                    value={formik.values.age}
                    onChange={formik.handleChange}
                    name="age"
                  />
                  {formik.touched.age && formik.errors.age && (
                    <div className="text-red-500 text-sm mt-1">
                      {formik.errors.age}
                    </div>
                  )}
                </div>

                <div>
                  <label className="text-sm/5 text-primary font-medium">
                    Gender
                  </label>
                  <SelectDropdown
                    options={genderOption}
                    value={formik.values.gender}
                    onChange={(value) => formik.setFieldValue("gender", value)}
                    placeholder="Select ..."
                  />
                  {formik.touched.gender && formik.errors.gender && (
                    <div className="text-red-500 text-sm mt-1">
                      {formik.errors.gender}
                    </div>
                  )}
                </div>

                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Description <span className="text-red-600">*</span>
                  </label>
                  <textarea
                    name="summary"
                    className="block w-full mt-2 p-3 border border-green-600/20 rounded-lg outline-none text-sm text-primary bg-transparent focus:border-green-600"
                    placeholder="Enter Description"
                    value={formik.values.summary}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  ></textarea>
                  {formik.touched.summary && formik.errors.summary ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.summary}
                    </div>
                  ) : null}
                </div>
              </form>
            </div>
          </div>

          {/* sidebar footer */}
          <div className="bg-white shadow-[0px_4px_43.4px_0px_#0000001A] px-5 py-2.5 grid grid-cols-2 gap-5 sticky bottom-0 z-10">
            <Button
              onClick={() => {
                formik.resetForm();
                setFreeSlot(false);
              }}
              variant="outlinedGreen"
            >
              Cancel
            </Button>
            <Button
              onClick={formik.handleSubmit}
              variant="filledGreen"
              disabled={loading}
            >
              {loading ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FreeSlotsSidebar;
