/* iOS-specific fixes */
@supports (-webkit-touch-callout: none) {
  /* Prevent rubber-band scrolling effect */
  html {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Fix for video controls */
  video::-webkit-media-controls {
    display: none !important;
  }

  /* Fix for input zoom on iOS */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  textarea {
    font-size: 16px !important;
  }

  /* Fix for background-attachment: fixed */
  .bg-fixed {
    background-attachment: scroll !important;
  }
}

/* Improve tap target sizes for iOS */
button, 
a {
  min-height: 44px;
  min-width: 44px;
}

/* Prevent text size adjustment on orientation change */
html {
  -webkit-text-size-adjust: 100%;
}

@keyframes errorHighlight {
  0% {
    background-color: rgba(239, 68, 68, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

.error-highlight {
  animation: errorHighlight 1s ease-out;
}

/* Sliding text animation */
@keyframes slide-left {
  0% {
    transform: translateX(100%);
  }
  25% {
    transform: translateX(0%);
  }
  75% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(-150%);
  }
}

.animate-slide-left {
  animation: slide-left 10s linear infinite;
}
