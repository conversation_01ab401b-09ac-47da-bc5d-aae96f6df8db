"use client";
import Button from "@/components/common/Button";
import DatePicker from "@/components/common/DatePicker";
import Input from "@/components/common/Input";
import TablePagination from "@/components/common/TablePagination";
import Tabs from "@/components/common/Tabs";
import ActivityCard from "@/components/dashboard/common/ActivityCard";
import SessionTBody, {
  Item,
} from "@/components/dashboard/common/table/SessionTBody";
import THeader from "@/components/dashboard/common/table/THeader";
import CommonModal from "@/components/dashboard/CommonModal";
import DaysSelectDropdown from "@/components/dashboard/DaysSelectDropdown";
import RescheduleSidebar from "@/components/dashboard/RescheduleSidebar";
import DashboardLayout from "@/layout/dashboard/DashboardLayout";
import {
  deleteSchedules,
  FilterParams,
  scheduleReminder,
  updatePayment,
  useGetScheduleCount,
  useGetSchedules,
} from "@/services/session.service";
import { useGetSettingData } from "@/services/setting.service";
import { fetcher, formatDate } from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import { FunnelSimple, MagnifyingGlass, X } from "@phosphor-icons/react";
import moment from "moment";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState, useCallback } from "react";
import { mutate } from "swr";

const sessionTabs = [
  { label: "Upcoming ", value: "upcoming" },
  { label: "Public Calendar ", value: "public-calendar" },
  { label: "Completed ", value: "completed" },
  { label: "Cancelled ", value: "cancelled" },
  { label: "All ", value: "" },
];

const sessionTableHeader = [
  "Name",
  "Date",
  "Time",
  "Payments",
  "status",
  "Appointment",
  "actions",
];

export interface SessionData {
  clientId?: {
    _id: string;
    name?: string;
    email?: string;
  };
  name?: string;
  email?: string;
  tillDate?: Date;
  fromDate?: Date | string;
  toDate?: Date | string;
  meetLink?: string;
  _id: string;
  recurrenceDates?: {
    _id: string;
  };
  fromPublicCalender?: boolean;
}

const Session = () => {
  // const searchParams =
  //   typeof window !== "undefined"
  //     ? new URLSearchParams(window.location.search)
  //     : null; // Access the query params

  // let clientId: string | null = null;

  // if (searchParams) {
  //   clientId = searchParams.get("client");
  // }

  const router = useRouter();

  const [clientId, setClientId] = useState<string | null>(null);

  const [currentPage, setCurrentPage] = useState(1);

  const pageSize = 5; // Set the page size as required

  const [isMonthsDropSelect, setIsMonthsDropSelect] = useState("Today");

  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [activeTable, setActiveTable] = useState(sessionTabs[0]);

  // sidebar
  const [isRescheduleSession, setIsRescheduleSession] = useState(false);

  // modals
  const [isFilter, setIsFilter] = useState(false);
  const [isUpdatePayment, setIsUpdatePayment] = useState(false);
  // const [isReminderModal, setIsReminderModal] = useState(false);
  const [isReminderMassageModal, setIsReminderMassageModal] = useState(false);
  const [isCanceledSessionModal, setIsCanceledSessionModal] = useState(false);
  const [isCancellationModal, setIsCancellationModal] = useState(false);
  const [isUpdatePaymentModal, setIsUpdatePaymentModal] = useState(false);
  const [isTerminatingModal, setIsTerminatingModal] = useState(false);
  const [singleSessionID, setSingleSessionID] = useState("");
  const [paymentID, setPaymentID] = useState("");
  const [rescheduleSessionID, setRescheduleSessionID] = useState("");
  const [isResetting, setIsResetting] = useState(false);
  const [loading, setLoading] = useState(false); // Loader state
  const [startDate, setStartDate] = useState(() => {
    const start = new Date();
    start.setHours(0, 0, 0, 0); // Set to start of the day (00:00:00)
    return start;
  });

  const [endDate, setEndDate] = useState(() => {
    const end = new Date();
    end.setHours(23, 59, 59, 999); // Set to end of the day (23:59:59)
    return end;
  });

  const [isUpdating, setIsUpdating] = useState(false);
  const [newAmount, setNewAmount] = useState<string | "">("");

  const [searchStartDate, setSearchStartDate] = useState<string | null>(null);
  const [searchEndDate, setSearchEndDate] = useState<string | null>(null);
  const [filterparams, setFilterparams] = useState<FilterParams>({}); // Control refetching

  const [isFilterApplied, setIsFilterApplied] = useState(false);

  // activity section start
  const { scheduleCountData } = useGetScheduleCount(startDate, endDate) as {
    scheduleCountData: {
      completed_sessions?: number;
      rescheduled_sessions?: number;
      cancelled_sessions?: number;
    };
  };

  const activity = [
    {
      title: "Completed Sessions",
      session: `${scheduleCountData?.completed_sessions || 0}`,
      percentage: "",
    },
    {
      title: "Reschedule Sessions",
      session: `${scheduleCountData?.rescheduled_sessions || 0}`,
      percentage: "",
    },
    {
      title: "Cancel Sessions",
      session: `${scheduleCountData?.cancelled_sessions || 0}`,
      percentage: "",
    },
  ];
  // activity section end
  // send remider start
  const [singleSessionData, setSingleSessionData] =
    useState<SessionData | null>(null);

  async function reminderSessionData() {
    try {
      setLoading(true); // Start loader
      const response = await scheduleReminder(singleSessionID);
      return response;
    } catch (error) {
      console.error("Failed to schedule reminder.", error);
    } finally {
      setLoading(false); // Stop loader
    }
  }

  const { therapistData } = useGetSettingData();

  // For public calendar tab, don't send status filter, only send fromPublicCalender filter
  const statusValue = activeTable.value === "public-calendar" ? "" : activeTable.value;

  const query =
    `pageSize=${pageSize}&pageNumber=${currentPage}&status=${statusValue}` +
    `&searchText=${debouncedSearchText}` +
    `&client=${clientId ?? ""}` +
    (filterparams &&
    filterparams?.searchStartDate &&
    filterparams?.searchEndDate
      ? `&startDate=${filterparams?.searchStartDate}&endDate=${filterparams?.searchEndDate}`
      : "") +
    (filterparams && filterparams.fromPublicCalender !== undefined
      ? `&fromPublicCalender=${filterparams.fromPublicCalender}`
      : "");

  const { sessionData, sessionLoading, sessionCount } = useGetSchedules({
    pageSize,
    currentPage,
    activeTable: statusValue,
    debouncedSearchText,
    clientId: clientId?.toString() ?? "",
    filterparams,
  });

  const totalPages = Math.ceil(sessionCount / pageSize);

  // Reset page to 1 when the active tab changes
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTable, isFilterApplied]);

  // Handle public calendar filter when tab is selected
  useEffect(() => {
    if (activeTable.value === "public-calendar") {
      setFilterparams(prev => ({
        ...prev,
        fromPublicCalender: true
      }));
      setIsFilterApplied(true);
    } else {
      // Remove public calendar filter when switching to other tabs
      setFilterparams(prev => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { fromPublicCalender, ...rest } = prev;
        // Only set isFilterApplied to false if no other filters are applied
        if (!rest.searchStartDate && !rest.searchEndDate && !clientId) {
          setIsFilterApplied(false);
        }
        return rest;
      });
    }
  }, [activeTable.value, clientId]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchText(searchText);
      setCurrentPage(1);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchText]);

  function handleModalTransition(
    closeModal: (value: boolean) => void,
    openModal: (value: boolean) => void
  ) {
    closeModal(false); // Close the currently open modal
    openModal(true); // Open the next modal
  }

  const deleteSingleSession = async (cancelled_session: string) => {
    try {
      setLoading(true);
      await deleteSchedules(singleSessionID, cancelled_session);

      const url = `${endpoints.sessions.schedules}?${query}`;
      mutate(url, async () => {
        await fetcher(url);
      });

      setIsUpdatePaymentModal(false);
      setIsTerminatingModal(false);
    } catch (error) {
      console.error("Failed to delete the session.", error);
    } finally {
      setLoading(false);
    }
  };

  const handleApplySearchFilter = () => {
    // Update the filterParams state, preserving existing filters
    setFilterparams(prev => ({
      ...prev,
      searchStartDate: searchStartDate
        ? moment(searchStartDate).startOf("day").toISOString()
        : "",
      searchEndDate: searchEndDate
        ? moment(searchEndDate).endOf("day").toISOString()
        : "",
    }));

    // Update isFilterApplied immediately
    setIsFilterApplied(true);

    // Close the modal after applying the filter
    setIsFilter(false);
  };

  // resetFilter
  const resetFilter = useCallback(() => {
    setIsResetting(true); // Start reset process
    // Clear clientId first to prevent useEffect from re-setting searchText
    if (clientId) {
      setClientId(null);
    }

    // Then, clear other search-related states
    setSearchStartDate(null);
    setSearchEndDate(null);
    setSearchText("");
    setFilterparams({});
    setIsFilterApplied(false); // Hide Reset button

    // Reset to "Upcoming" tab if currently on "Public Calendar" tab
    if (activeTable.value === "public-calendar") {
      setActiveTable(sessionTabs[0]); // Reset to "Upcoming" tab (now first in array)
    }

    // Navigate to the main session page
    router.push(`/session`);
  }, [clientId, activeTable.value, router]);

  // Keep track of whether we've already processed a conflict date
  const [processedConflictDate, setProcessedConflictDate] = useState<string | null>(null);

  useEffect(() => {
    if (!isResetting && typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      const newClientId = searchParams.get("client") || searchParams.get("clientId");
      const conflictDate = searchParams.get("conflictDate");
      const searchClient = searchParams.get("searchClient");

      // Handle search client parameter (from conflict modal redirection)
      if (searchClient && !searchText) {
        const decodedClientName = decodeURIComponent(searchClient);
        setSearchText(decodedClientName);

        // Clear the searchClient parameter from URL after setting search text
        // This allows the search to persist across pagination while cleaning the URL
        setTimeout(() => {
          const url = new URL(window.location.href);
          url.searchParams.delete("searchClient");
          window.history.replaceState({}, "", url.toString());
        }, 500);
      }

      // Update clientId if it has changed
      if (newClientId !== clientId) {
        setClientId(newClientId);
         // If this is a clientId from calendar sync conflict, clear it from URL after a short delay
        // so that on refresh, the page shows default state
        if (searchParams.get("clientId")) {
          setTimeout(() => {
            const url = new URL(window.location.href);
            url.searchParams.delete("clientId");
            window.history.replaceState({}, "", url.toString());
          }, 1000); // Wait 1 second to allow filtering to take effect
        }
      }

      // If we've already processed this conflict date and the sidebar is open, skip processing
      if (conflictDate && processedConflictDate === conflictDate && isRescheduleSession) {
        return;
      }

      // Update the processed conflict date
      if (conflictDate) {
        setProcessedConflictDate(conflictDate);
      }

      // Handle conflict date parameter - find the session with this date and open reschedule sidebar
      if (conflictDate) {
        // Only proceed if we have session data and haven't already opened the sidebar
        if (sessionData && sessionData.length > 0 && !isRescheduleSession) {
          // Find the session with the conflict date
          const conflictSession = (sessionData as Array<SessionData>).find((session) => {
            if (session.fromDate) {
              const sessionDate = moment(session.fromDate).format("YYYY-MM-DD");
              return sessionDate === conflictDate;
            }
            return false;
          }); // No need for type assertion since we're casting the array

          // If found, open the reschedule sidebar for this session
          if (conflictSession) {
            // Make sure we have a valid session ID
            if (conflictSession._id) {

              // Check if the session has recurrenceDates._id
              const hasRecurrenceDates = !!(conflictSession.recurrenceDates && conflictSession.recurrenceDates._id);

              // Use recurrenceDates._id if available, otherwise use session._id
              const sessionIdToUse = hasRecurrenceDates && conflictSession.recurrenceDates
                ? conflictSession.recurrenceDates._id
                : conflictSession._id;

              // Store the session data and ID
              setSingleSessionData(conflictSession as unknown as SessionData);
              setRescheduleSessionID(sessionIdToUse);

              // Open the reschedule sidebar
              setIsRescheduleSession(true);
            } else {
              console.error("Conflict session found but no ID available");
            }
          } else {
            console.error("No session found with date:", conflictDate);

            // Remove the conflictDate parameter from the URL
            if (typeof window !== "undefined") {
              const url = new URL(window.location.href);
              url.searchParams.delete("conflictDate");
              window.history.replaceState({}, "", url.toString());
            }
          }
        } else if (!sessionData || sessionData.length === 0) {
          // Only show this error if we truly have no session data and the sidebar isn't open
          if (!isRescheduleSession) {
            console.error("No sessions available to search for conflict date:", conflictDate);
          }
        }
        // If isRescheduleSession is already true, we don't need to do anything
      }
    }
  }, [router, clientId, resetFilter, isResetting, sessionData, isRescheduleSession, processedConflictDate, searchText]); // Run effect whenever relevant state changes

  useEffect(() => {
    if (clientId) {
      setIsFilterApplied(true);

      if (
        (sessionData[0] as { client?: { name: string } })?.client?.name &&
        !searchText
      ) {
        // setSearchText(sessionData[0]?.client?.name);

        setSearchText(
          (sessionData[0] as { client?: { name: string } })?.client?.name || ""
        );
      }
    } else if (!filterparams && !searchText) {
      setIsFilterApplied(false);
    }
  }, [clientId, sessionData, searchText, filterparams]);

  // Show filter applied state when search text is present
  useEffect(() => {
    if (searchText && !clientId && !filterparams?.searchStartDate && !filterparams?.searchEndDate && activeTable.value !== "public-calendar") {
      setIsFilterApplied(true);
    }
  }, [searchText, clientId, filterparams, activeTable.value]);

  return (
    <DashboardLayout>
      <div className="bg-white mt-5 rounded-base overflow-hidden">
        {/* Session header */}

        {/* activity section */}
        <div className="md:p-5 p-3">
          <div className="flex items-center justify-between">
            <h2 className="text-lg/5 sm:text-xl/6  text-primary font-semibold">
              Activities
            </h2>
            <DaysSelectDropdown
              value={isMonthsDropSelect}
              onChange={(value: unknown) =>
                setIsMonthsDropSelect(value as string)
              }
              DropClass=""
              setStartDate={setStartDate}
              setEndDate={setEndDate}
            />
          </div>
          <div className="pt-5 grid sm:grid-cols-3 gap-5">
            {activity?.map((items, index) => (
              <ActivityCard
                key={index}
                title={items.title}
                count={items.session}
                percentage=""
                borderColor={
                  index === 0
                    ? "border-[#48A400]"
                    : index === 1
                    ? "border-[#1339FF]"
                    : "border-[#FF5C00]"
                }
              />
            ))}
          </div>
        </div>

        {/* table */}
        <div className="p-5">
          <div className="flex flex-wrap gap-5 items-center justify-between">
            <Tabs
              tabs={sessionTabs}
              activeTab={activeTable.label}
              setActiveTab={(tab) => setActiveTable(tab)}
              sessionCount={sessionCount}
            />
            <div className="flex items-center gap-2 max-w-[391px] sm:min-w-[391px] w-full sm:py-15px sm:px-5 p-3 border border-[#9B9DB7] rounded-full text-xs text-primary">
              <MagnifyingGlass className="text-primary/50 min-w-5 w-5 h-5" />
              <input
                type="search"
                placeholder="Search your client name and id"
                className="outline-none w-full placeholder:text-primary/50"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
              />
              <span className="text-primary/50">|</span>
              <div className="flex items-center bg-green-600/5 py-1 px-2.5 rounded-full gap-3">
                <FunnelSimple
                  size={20}
                  className="text-green-600 cursor-pointer"
                  onClick={() => {
                    setIsFilter(!isFilter);
                  }}
                />
                {isFilterApplied && (
                  <div
                    onClick={resetFilter}
                    className="w-5 h-5 rounded-full border border-primary/20 text-primary flex items-center justify-center bg-white cursor-pointer"
                  >
                    <X size={12} />
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="pt-10">
            <div className="w-full border border-green-600/25 rounded-base overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full  bg-white">
                  <THeader data={sessionTableHeader} />
                  <SessionTBody
                    TableData={sessionData}
                    sessionLoading={sessionLoading}
                    setIsRescheduleSession={setIsRescheduleSession}
                    isRescheduleSession={isRescheduleSession}
                    setIsUpdatePayment={setIsUpdatePayment}
                    isUpdatePayment={isUpdatePayment}
                    setIsReminderModal={setIsReminderMassageModal}
                    setIsCanceledSessionModal={setIsCanceledSessionModal}
                    isCanceledSessionModal={isCanceledSessionModal}
                    setSingleSessionID={setSingleSessionID}
                    setPaymentID={setPaymentID}
                    setRescheduleSessionID={setRescheduleSessionID}
                    setSingleSessionData={(item: Item) =>
                      setSingleSessionData(item as unknown as SessionData)
                    }
                    therapistData={therapistData}
                  />
                </table>
              </div>
              <TablePagination
                totalPages={totalPages}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            </div>
          </div>
        </div>
      </div>
      {/* ====== Side Bars ====== */}
      {/* Reschedule Session sidebar */}
      <RescheduleSidebar
        isRescheduleSession={isRescheduleSession}
        setIsRescheduleSession={setIsRescheduleSession}
        singleSessionID={rescheduleSessionID}
        query={query}
        singleSessionData={singleSessionData}
      />
      {/* ====== Session modals ====== */}
      {/* Filter Modal */}
      <CommonModal title="Filter" isClose={isFilter} setIsClose={setIsFilter}>
        <div className="py-7.5 space-y-5">
          <div>
            <label className="text-base/5 text-primary font-medium">
              Start Date
            </label>
            <DatePicker
              value={searchStartDate ?? ""}
              // value={searchStartDate ? searchStartDate.toISOString() : ""}
              placeholder={`DD/MM/YYYY`}
              className={`!mt-3`}
              onChange={(date) => {
                setSearchStartDate(date);
              }}
            />
          </div>
          <div>
            <label className="text-base/5 text-primary font-medium">
              End Date
            </label>
            <DatePicker
              value={searchEndDate ?? ""}
              minDate={searchStartDate ?? ""}
              placeholder={`DD/MM/YYYY`}
              className={`!mt-3`}
              onChange={(date) => {
                setSearchEndDate(date);
              }}
            />
          </div>
        </div>
        <div className="flex items-center justify-end gap-3.5">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() => {
              setIsFilter(false);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px]`}
            disabled={!searchStartDate || !searchEndDate}
            onClick={handleApplySearchFilter}
          >
            Apply
          </Button>
        </div>
      </CommonModal>
      {/* Upadate Payment Modal */}
      <CommonModal
        title="Update Payment"
        isClose={isUpdatePayment}
        setIsClose={setIsUpdatePayment}
      >
        <div className="py-7.5 space-y-5">
          <div>
            <label className="text-base/5 text-primary font-medium">
              Old Amount
            </label>
            <Input
              value=""
              onChange={() => {}}
              name="oldAmount"
              type="number"
              placeholder="0"
              icon="rup"
              disabled={true}
            />
          </div>
          <div>
            <label className="text-base/5 text-primary font-medium">
              New Amount
            </label>
            <Input
              value={newAmount}
              onChange={(e) => setNewAmount(e.target.value)}
              name="newAmount"
              type="number"
              placeholder="0"
              icon="rup"
            />
          </div>
        </div>
        <div className="flex items-center justify-end gap-3.5">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() => setIsUpdatePayment(false)}
            disabled={isUpdating} // Disable button when updating
          >
            Cancel
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px]`}
            onClick={async () => {
              if (newAmount) {
                setIsUpdating(true);
                try {
                  await updatePayment(paymentID, newAmount);

                  const url = `${endpoints.sessions.schedules}?${query}`;

                  // Log the URL to verify it's correct

                  mutate(url, async () => {
                    // This callback can be used to fetch updated data if needed
                    await fetcher(url);
                  });

                  setIsUpdatePayment(false);
                } catch (error) {
                  console.error("Payment update failed:", error);
                } finally {
                  setIsUpdating(false);
                }
              }
            }}
            disabled={isUpdating} // Disable button when updating
          >
            {isUpdating ? "Updating..." : "Update"}
          </Button>
        </div>
      </CommonModal>
      {/* Reminder Modal */}
      {/* <CommonModal
        title="Reminder"
        isClose={isReminderModal}
        setIsClose={setIsReminderModal}
      >
        <div className="pt-30px space-y-2.5">
          <label className="flex justify-between items-center text-sm/5 text-gray-500">
            Session Reminder
            <input type="radio" name="reminder" className="w-4.5 h-4.5" />
          </label>
          <label className="flex justify-between items-center text-sm/5 text-gray-500">
            Payment Session
            <input type="radio" name="reminder" className="w-4.5 h-4.5" />
          </label>
          <label className="flex justify-between items-center text-sm/5 text-gray-500">
            Set Both
            <input type="radio" name="reminder" className="w-4.5 h-4.5" />
          </label>
        </div>
        <div className="flex items-center justify-end gap-3.5 pt-[34px]">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() => {
              setIsReminderModal(false);
            }}
          >
            No
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px]`}
            onClick={() => {
              handleModalTransition(
                setIsReminderModal,
                setIsReminderMassageModal
              );
            }}
          >
            Yes
          </Button>
        </div>
      </CommonModal> */}
      {/* Reminder massage Modal */}
      <CommonModal
        title="Send reminder"
        isClose={isReminderMassageModal}
        setIsClose={(isClose) => {
          if (!loading) {
            setIsReminderMassageModal(isClose); // Prevent manual close while loading
          }
        }}
        className={`!max-w-[450px]`}
      >
        <div className="text-base/7 text-primary pt-5">
          <p>
            To:{" "}
            <span className="text-green-600 capitalize">
              {singleSessionData?.clientId?.name || "-"}
            </span>{" "}
            <span className="text-green-600 lowercase">
              {`<${singleSessionData?.email || "-"}>`}
            </span>
          </p>
          <p>
            Subject: <span className="font-semibold">Session Reminder 🔔</span>
          </p>
          <p className="text-green-600 font-semibold capitalize">
            Hi {singleSessionData?.clientId?.name || "-"}
          </p>
          <p className="pt-5">
            Your appointment with{" "}
            <span className="font-semibold">{therapistData?.name}</span> is
            scheduled for{" "}
            <span className="font-semibold">
              {singleSessionData &&
                formatDate(
                  moment(singleSessionData?.fromDate).format("YYYY-MM-DD")
                )}{" "}
              at{" "}
              {singleSessionData &&
                moment(singleSessionData?.fromDate).format("HH:mm")}
            </span>
          </p>
          {singleSessionData?.meetLink && (
            <p className="pt-3">
              You can use this link to join the call:{" "}
              <Link
                href={`${singleSessionData?.meetLink}`}
                className="underline text-green-600 font-medium"
              >
                {singleSessionData?.meetLink}
              </Link>
            </p>
          )}
        </div>
        <Button
          variant="filledGreen"
          className={`w-full mt-7.5 ${
            loading ? "opacity-50 cursor-not-allowed" : ""
          }`}
          onClick={() => {
            if (!loading) {
              reminderSessionData().then(() => {
                setIsReminderMassageModal(false); // Close modal after sending reminder
              });
            }
          }}
          disabled={loading} // Disable button during loading
        >
          {loading ? "Sending..." : "Send Reminder"} {/* Update button text */}
        </Button>
      </CommonModal>
      {/* Session Canceled */}
      <CommonModal
        title="Session Canceled"
        isClose={isCanceledSessionModal}
        setIsClose={setIsCanceledSessionModal}
      >
        <p className="text-gray-500 text-sm/5 pt-5 max-w-[465px]">
          Are you sure you want to mark this session as a Cancel ? This action
          will notify the customer and update your records accordingly.
        </p>
        <div className="flex items-center justify-end gap-3.5 pt-[34px]">
          <Button
            variant="outlinedGreen"
            onClick={() =>
              handleModalTransition(
                setIsCanceledSessionModal,
                setIsTerminatingModal
              )
            }
            className={`min-w-[157px]`}
          >
            cancel entire slot
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px]`}
            onClick={() =>
              handleModalTransition(
                setIsCanceledSessionModal,
                setIsCancellationModal
              )
            }
          >
            this session only
          </Button>
        </div>
      </CommonModal>
      {/* Cancellation Fees */}
      <CommonModal
        title="What do you want to do?"
        isClose={isCancellationModal}
        setIsClose={setIsCancellationModal}
      >
        <div className="flex items-center justify-end gap-3.5 pt-[34px]">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() =>
              handleModalTransition(
                setIsCancellationModal,
                setIsUpdatePaymentModal
              )
            }
          >
            Free Cancellation
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px]`}
            onClick={() => {
              handleModalTransition(
                setIsCancellationModal,
                setIsUpdatePaymentModal
              );
            }}
          >
            Paid Cancellation
          </Button>
        </div>
      </CommonModal>
      {/* Update Payment Session */}
      <CommonModal
        title="Update Payment Session"
        isClose={isUpdatePaymentModal}
        setIsClose={(isClose) => {
          if (!loading) {
            setIsUpdatePaymentModal(isClose); // Only allow closing if not loading
          }
        }}
      >
        <p className="text-gray-500 text-sm/5 pt-5 max-w-[465px]">
          ⚠️ We are calling off this session. After you have got it, remember to
          update the same information in your payment.
        </p>
        <div className="text-end pt-[34px]">
          <Button
            variant="filledGreen"
            className={`min-w-[157px] ${
              loading ? "opacity-50 cursor-not-allowed" : ""
            }`}
            onClick={() => deleteSingleSession("this session only")}
            disabled={loading} // Disable button when loading
          >
            {loading ? "Processing..." : "Okay, got it"}
          </Button>
        </div>
      </CommonModal>
      {/* Are you terminating the client */}
      <CommonModal
        title="Are you terminating the client"
        isClose={isTerminatingModal}
        setIsClose={(isClose) => {
          if (!loading) {
            setIsTerminatingModal(isClose);
          }
        }}
      >
        <p className="text-gray-500 text-sm/5  pt-5 max-w-[465px]">
          This action will end all services with the client. Please confirm to
          proceed.
        </p>
        <div className="flex items-center justify-end gap-3.5 pt-[34px]">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() => {
              setIsTerminatingModal(false);
              setIsRescheduleSession(true);
            }}
          >
            No
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px] ${
              loading ? "opacity-50 cursor-not-allowed" : ""
            }`}
            onClick={() => deleteSingleSession("cancel entire slot")}
            disabled={loading}
          >
            {loading ? "Processing..." : "Yes"}
          </Button>
        </div>
      </CommonModal>
    </DashboardLayout>
  );
};

export default Session;