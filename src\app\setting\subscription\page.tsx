"use client";
import Button from "@/components/common/Button";
import { useSubscriptionData } from "@/context/SubscriptionContext";
import { useFetchTherapistData } from "@/context/TherapistContext";
import { MomentHelper } from "@/helper/MomentHelper";
import SettingLayout from "@/layout/dashboard/SettingLayout";
import { useGetValidSubscription } from "@/services/dashboard.service";
import {
  activateFreeTrialSubscription,
  checkTherapistSubscription,
  useGetSubscriptionPlan,
  usePaymentHistory,
} from "@/services/setting.service";
import { formatDate } from "@/utils/axios";
import { checkoutRazorpay } from "@/utils/razorpay";
import Image from "next/image";
import { useEffect, useState } from "react";
import { AuthService } from "@/services/auth.service";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import SubscriptionInfoModal from "@/components/common/SubscriptionInfoModal";
import ActivationLoader from "@/components/subscription/ActivationLoader";

const subscriptionPlan = [
  {
    title: "One-Click Google Calendar Sync",
    features:
      "Import all your existing sessions—clients, supervision, everything—in one go.",
  },
  {
    title: "Smart Income & Cancellation Tracking",
    features:
      "Stay on top of your earnings and missed sessions, all from a single dashboard.",
  },
  {
    title: "Client List, Sorted",
    features:
      "Keep track of active and inactive clients, all in one organised view.",
  },
  {
    title: "Integrated Booking Calendar",
    features:
      "Say goodbye to scheduling chaos—both new and old bookings in one place.",
  },
  {
    title: "Automatic Reminders",
    features:
      "Email and WhatsApp nudges sent to clients, so you don’t have to follow up manually.",
  },
  {
    title: "Full Income Overview",
    features:
      "See what you earned today, this month, or the whole year—at a glance.",
  },
];

// const paymentHistory = [
//   {
//     plan: "Satir",
//     amount: "₹1,000",
//     status: true,
//     validity: "30",
//     subscriptionDate: "5,Aug 2024 to 03,Nov 2024",
//     purchaseDate: "5,July 2024",
//   },
//   {
//     plan: "Winnicott",
//     amount: "₹500",
//     status: false,
//     validity: "90",
//     subscriptionDate: "5,Aug 2024 to 03,Nov 2024",
//     purchaseDate: "5,July 2024",
//   },
//   {
//     plan: "Free Trial Plan",
//     amount: "0",
//     status: true,
//     validity: "30",
//     subscriptionDate: "5,Aug 2024 to 03,Nov 2024",
//     purchaseDate: "5,July 2024",
//   },
// ];

interface SubscriptionId {
  name: string;
  price: number;
  status: string;
  validDays: string;
  subscriptionType: string;
  isAnnual?: boolean;
  isMonthly?: boolean;
  isTrialIncluded?: boolean;
}

export interface PaymentHistoryItem {
  subscriptionId: SubscriptionId;
  validFrom: string;
  validTill: string;
  createdAt: string;
}

interface SubscriptionPlanItem {
  name: string;
  price: number;
  validDays?: number;
  subscriptionType: string;
  currency: string;
  description?: string;
  _id: string;
  isTrialIncluded: boolean;
  isMonthly: boolean;
  isAnnual: boolean;
}

interface SubscriptionPlanItemExtended extends SubscriptionPlanItem {
  fullName?: string;
}

interface Address {
  pincode?: string;
  state?: string;
}

interface TherapistData {
  name?: string;
  email?: string;
  phone?: string;
  hasUsedTrial?: boolean;
  _id?: string;
  panCard?: string;
  address?: Address;
}

interface SubscriptionDetails {
  _id?: string;
  name?: string;
  price?: number;
  validDays?: number;
  [key: string]: string | number | undefined;
}

const Subscription = () => {
  const [paymentSuccessId, setPaymentSuccessId] = useState<string | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [modalPlan, setModalPlan] =
    useState<SubscriptionPlanItemExtended | null>(null);
  const [hasStartedTrial, setHasStartedTrial] = useState(false);
  const [updatedPlans, setUpdatedPlans] = useState<
    SubscriptionPlanItemExtended[]
  >([]);
  const [freeTrialPlan, setFreeTrialPlan] =
    useState<SubscriptionPlanItem | null>(null);
  const { refetchValidSubscription, validSubscriptionData } =
    useGetValidSubscription();
  const {
    paymentHistoryData = [],
    paymentHistoryLoading,
    refetchPaymentHistory,
  } = usePaymentHistory() as {
    paymentHistoryData: PaymentHistoryItem[];
    paymentHistoryLoading: boolean;
    refetchPaymentHistory: () => void;
  };

  const [planType, setPlanType] = useState<"monthly" | "annual">("monthly");
  const { subscriptionPlanData } = useGetSubscriptionPlan() as {
    subscriptionPlanData: SubscriptionPlanItem[];
  };
  const { therapistData, refetchTherapistData } = (useFetchTherapistData() || {
    therapistData: null,
  }) as {
    therapistData: TherapistData | null;
    refetchTherapistData?: () => Promise<void>;
  };

  const router = useRouter();
  const { subscriptionDetails, setSubscriptionDetails } =
    (useSubscriptionData() || {
      subscriptionDetails: null,
      setSubscriptionDetails: () => {},
    }) as {
      subscriptionDetails: SubscriptionDetails | null;
      setSubscriptionDetails: (data: SubscriptionDetails) => void;
    };
  const [activePlanId, setActivePlanId] = useState<string | null>(null);
  const [showSubscriptionInfoModal, setShowSubscriptionInfoModal] =
    useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [isActivating, setIsActivating] = useState(false);

  async function reloadSubscriptionDetails() {
    try {
      const res = await checkTherapistSubscription();

      if (res.status === 200) {
        const sub = res.data;

        // Skip setting subscription if it's a free trial plan
        if (!(sub?.isTrialIncluded && sub?.price === 0)) {
          setSubscriptionDetails(sub);
        }

        if (refetchPaymentHistory) {
          refetchPaymentHistory();
        }
      }
    } catch (err) {
      console.log("Error reloading subscription details:", err);
    }
  }

  const createPayment = ({
    subscriptionName,
    subscriptionId,
    onSuccess,
  }: {
    subscriptionName: string;
    subscriptionId: string;
    onSuccess?: () => void;
  }) => {
    const params = {
      name: therapistData?.name || "",
      email: therapistData?.email || "",
      phone_number: therapistData?.phone || "",
      subscription_name: subscriptionName,
      subscription_id: subscriptionId,
      setIsActivating: setIsActivating, // Pass the loader state setter
      reloadSubscriptionDetails: async () => {
        await reloadSubscriptionDetails();

        if (!hasStartedTrial) {
          await markTrialUsed();
          await refetchTherapistData?.();
          setHasStartedTrial(true);
        }

        refetchValidSubscription();
        setPaymentSuccessId(subscriptionId);
        onSuccess?.();
        router.push("/dashboard");
      },
    };
    checkoutRazorpay(params);
  };

  useEffect(() => {
    // Ensure that subscriptionPlanData exists before processing
    if (!subscriptionPlanData) return;

    setHasStartedTrial(therapistData?.hasUsedTrial ?? false);

    // 1. Identify the actual free trial plan (safe access)
    const freeTrial =
      subscriptionPlanData.find(
        (plan) => plan?.subscriptionType === "TRIAL PLAN" && plan?.price === 0
      ) || null;
    setFreeTrialPlan(freeTrial);

    // 2. Filter out actual free trial plan from main plans shown to user
    const filteredPlans = subscriptionPlanData.filter(
      (plan) => !(plan?.subscriptionType === "TRIAL PLAN" && plan?.price === 0)
    );
    setUpdatedPlans(filteredPlans);

    // 3. Detect active non-trial subscriptions
    const activePaidPlans =
      paymentHistoryData?.filter((entry) => {
        const sub = entry?.subscriptionId;
        return (
          typeof sub === "object" &&
          sub?.subscriptionType !== "TRIAL PLAN" &&
          typeof sub?.status === "string" &&
          sub?.status?.toLowerCase() === "active"
        );
      }) || [];

    const preferredPlan =
      activePaidPlans.find((entry) => entry?.subscriptionId?.isAnnual) ||
      activePaidPlans[0];

    if (
      preferredPlan?.subscriptionId &&
      typeof preferredPlan?.subscriptionId === "object"
    ) {
      const matchingPlan = subscriptionPlanData.find(
        (plan) =>
          plan?.name === preferredPlan?.subscriptionId?.name &&
          plan?.price === preferredPlan?.subscriptionId?.price
      );

      if (matchingPlan?._id) {
        setActivePlanId(matchingPlan._id);
      }
    }
  }, [
    subscriptionPlanData,
    therapistData,
    subscriptionDetails,
    paymentHistoryData,
  ]);

  const markTrialUsed = async () => {
    if (!therapistData?._id) return;
    try {
      await AuthService.updateTherapistProfile(therapistData._id, {
        hasUsedTrial: true,
      });
    } catch (error) {
      console.error("Failed to mark trial as used:", error);
    }
  };

  useEffect(() => {
    if (formSubmitted) {
      setShowConfirmModal(true);
    }
  }, [formSubmitted]); // This will trigger when formSubmitted changes

  const handleSubscriptionClick = async (
    item: SubscriptionPlanItemExtended
  ) => {
    // Ensure item is not null and has the required properties
    if (!item || !item._id) return;

    // Directly check for free trial plan based on subscriptionType and price
    const isTrialPlan = item?.isTrialIncluded;

    const therapistHasRequiredData =
      therapistData?.panCard && therapistData?.address?.pincode;

    // Skip the modal and directly activate the free trial plan
    if (isTrialPlan && !hasStartedTrial) {
      if (freeTrialPlan?._id) {
        try {
          setIsActivating(true); // Show loader

          // Add 3-second delay
          await new Promise((resolve) => setTimeout(resolve, 3000));

          const response = await activateFreeTrialSubscription(
            freeTrialPlan._id
          );

          if (response.status === 200) {
            await markTrialUsed();
            await refetchTherapistData?.();
            setHasStartedTrial(true);
            setActivePlanId(freeTrialPlan._id);
            await reloadSubscriptionDetails();
            refetchValidSubscription();
            toast.success("Free trial activated!");
            router.push("/dashboard");
          } else {
            toast.error("Free trial activation failed");
          }
        } catch (error) {
          console.error("Error activating free trial:", error);
          toast.error("Error activating free trial");
        } finally {
          setIsActivating(false); // Hide loader
        }
      } else {
        toast.error("Free trial plan not found");
      }
      return;
    }

    setModalPlan(item);
    setFormSubmitted(false);

    if (therapistHasRequiredData) {
      setShowConfirmModal(true);
    } else {
      setShowSubscriptionInfoModal(true);
    }
  };

  const handleSubscriptionInfoModalClose = () => {
    setShowSubscriptionInfoModal(false);
    if (formSubmitted) {
      setShowConfirmModal(true);
    }
  };

  const handleFormSubmitted = () => {
    setFormSubmitted(true);
  };

  // Filter plans based on planType
  const filteredPlans = updatedPlans?.filter((plan) =>
    planType === "monthly" ? plan.isMonthly : !plan.isMonthly
  );

  return (
    <SettingLayout>
      {/* Show loader for both free trial and paid subscription activation */}
      {isActivating && <ActivationLoader />}
      <div>
        {/* Header - Desktop */}
        <div className="md:flex justify-between items-center">
          <h1 className="text-xl_30 font-semibold text-primary">
            Subscription
          </h1>
          <div className="hidden lg:block text-[12px] font-semibold">
            Days remaining:{" "}
            <span className="text-orange-500">
              {validSubscriptionData?.validDays ?? 0} days
            </span>
          </div>
        </div>

        {/* Title Section */}
        <div className="lg:hidden mt-8 text-center md:text-left">
          <h2 className="text-[22px] leading-7 font-semibold text-primary">
            <span className="text-green-600">Simple plan</span> for everything
          </h2>
          <p className="text-sm/5 font-medium text-primary pt-2">
            Find the best plan and the best price that right for you
          </p>
        </div>

        {/* Header - Mobile */}
        <div className="lg:hidden text-center">
          <div className="text-[12px] font-semibold mt-2">
            Days remaining:{" "}
            <span className="text-orange-500">
              {validSubscriptionData?.validDays ?? 0} days
            </span>
          </div>
        </div>

        {/* Toggle Buttons - Mobile Only */}
        <div className="lg:hidden flex justify-center gap-3 mt-4">
          <button
            className={`px-4 py-2 rounded-full font-semibold border ${
              planType === "monthly"
                ? "bg-primary text-white"
                : "bg-white text-primary"
            }`}
            onClick={() => setPlanType("monthly")}
          >
            Monthly
          </button>
          <button
            className={`px-4 py-2 rounded-full font-semibold border ${
              planType === "annual"
                ? "bg-primary text-white"
                : "bg-white text-primary"
            }`}
            onClick={() => setPlanType("annual")}
          >
            Annually
          </button>
        </div>

        {/* Desktop View - Original subscription content */}
        <div className="hidden lg:block mt-5 w-full border border-primary/10 rounded-base overflow-x-auto">
          <table className="w-full">
            <thead className="text-left ">
              <tr className="">
                <th className="w-[300px] min-w-[300px] pt-3.5 pb-5 px-5">
                  <div className="max-w-[224px]">
                    <h2 className="text-[22px] leading-7 font-semibold text-primary">
                      <span className="text-green-600">Simple plan</span> for
                      everything
                    </h2>
                    <p className="text-sm/5 font-medium text-primary pt-2">
                      Find the best plan and the best price that right for you
                    </p>
                  </div>
                </th>
                {updatedPlans?.map(
                  (item: SubscriptionPlanItemExtended, index: number) => {
                    const isCurrentPlan = activePlanId === item._id;
                    const isJustPaid = paymentSuccessId === item._id;
                    const isMaxValidity =
                      (updatedPlans.find((p) => p._id === activePlanId)
                        ?.validDays ?? 0) >= 360;
                    const activePlan = updatedPlans.find(
                      (p) => p._id === activePlanId
                    );
                    const isAnnualActive = activePlan?.isAnnual;
                    const isMonthlyPlan = item.isMonthly;

                    // Check if the plan includes a trial
                    const isTrialIncluded = item.isTrialIncluded;

                    return (
                      <th
                        className="w-[255px] pr-2 min-w-[255px] pt-3.5 pb-5"
                        key={index}
                      >
                        <div
                          className={`w-full h-[249px] flex flex-col justify-center items-center ${
                            index === 1 ? "bg-primary rounded-base" : ""
                          }`}
                        >
                          <p
                            className={`text-xl/6 font-bold text-center break-words px-2 leading-tight ${
                              index === 1 ? "text-white" : "text-primary"
                            }`}
                          >
                            {item.name}
                          </p>
                          <p
                            className={`text-[16px] font-medium text-center break-words pt-2 px-2 leading-tight ${
                              index === 1 ? "text-white" : "text-primary"
                            }`}
                          >
                            {item.isMonthly
                              ? "Quick start with"
                              : "15 day free trial and then"}
                          </p>

                          <p
                            className={`pt-4 text-[32px] leading-9 font-semibold ${
                              index === 1 ? "text-white" : "text-primary"
                            }`}
                          >
                            {item.currency === "INR" ? "₹" : "$"}
                            {item.price}/{item.isMonthly ? "month" : "year"}
                          </p>

                          <p
                            className={`text-[16px] font-bold ${
                              index === 1 ? "text-white" : "text-primary"
                            }`}
                          >
                            (incl. GST)
                          </p>

                          <Button
                            type="button"
                            variant={index === 1 ? "filled" : "lightGreen"}
                            className="!text-base_18 w-[180px] mt-10 text-center"
                            onClick={() => handleSubscriptionClick(item)}
                            disabled={
                              isCurrentPlan ||
                              isJustPaid ||
                              isMaxValidity ||
                              (isMonthlyPlan && isAnnualActive) ||
                              (hasStartedTrial && isMonthlyPlan)
                            }
                          >
                            {isCurrentPlan
                              ? "Current Plan"
                              : isTrialIncluded && !hasStartedTrial
                              ? "Start Free Trial"
                              : "Pay Now"}
                          </Button>
                        </div>
                      </th>
                    );
                  }
                )}

                {/* <th className="w-[255px] pt-3.5 pb-5">
                  <div className="w-full h-[249px] flex flex-col justify-center items-center bg-primary rounded-lg">
                    <p className="text-xl/6 font-medium text-white">Freud</p>
                    <p className="pt-8 text-[32px] leading-9 font-semibold text-white">
                      ₹33.33{" "}
                      <span className="text-xs font-medium">/ Per day</span>
                    </p>
                    <Button
                      variant="filled"
                      className="!text-base_18 w-[150px] font-semibold mt-10"
                    >
                      PAY ₹1000
                    </Button>
                  </div>
                </th>
                <th className="w-[255px] pt-3.5 pb-5">
                  <div className="w-full h-[249px] flex flex-col justify-center items-center">
                    <p className="text-xl/6 font-medium text-primary">
                      Winnicott
                    </p>
                    <p className="pt-8 text-[32px] leading-9 font-semibold text-primary">
                      ₹16.67{" "}
                      <span className="text-xs font-medium">/ Per day</span>
                    </p>
                    <Button
                      variant="lightGreen"
                      className="!text-base_18 w-[150px] mt-10"
                    >
                      PAY ₹500
                    </Button>
                  </div>
                </th> */}
              </tr>
            </thead>
            <tbody>
              {subscriptionPlan?.map((item, index) => (
                <tr
                  key={index}
                  className={index % 2 === 1 ? "" : "bg-green-600/10"}
                >
                  <td className="pl-5 py-6.5">
                    <p className="text-base_18 text-primary font-medium">
                      {item.title}
                    </p>
                  </td>
                  <td colSpan={2}>
                    <p className="text-left text-[12px] lg:pl-12 text-primary">
                      {item.features}
                    </p>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Mobile View */}
        <div className="lg:hidden mt-5 space-y-6">
          {filteredPlans?.map(
            (item: SubscriptionPlanItemExtended, index: number) => {
              const isCurrentPlan = activePlanId === item._id;
              const isJustPaid = paymentSuccessId === item._id;
              const isMaxValidity =
                (updatedPlans.find((p) => p._id === activePlanId)?.validDays ??
                  0) >= 360;
              const activePlan = updatedPlans.find(
                (p) => p._id === activePlanId
              );
              const isAnnualActive = activePlan?.isAnnual;
              const isMonthlyPlan = item.isMonthly;
              const isTrialIncluded = item.isTrialIncluded;

              return (
                <div
                  key={index}
                  className={`rounded-lg p-4 ${
                    index === 1
                      ? "bg-primary text-white"
                      : "bg-white border border-primary/10"
                  }`}
                >
                  {/* Plan Header */}
                  <div className="text-center">
                    <h3
                      className={`text-xl font-bold ${
                        index === 1 ? "text-white" : "text-primary"
                      }`}
                    >
                      {item.name}
                    </h3>
                    <p
                      className={`mt-2 text-sm ${
                        index === 1 ? "text-white" : "text-primary"
                      }`}
                    >
                      {item.isMonthly
                        ? "Quick start with"
                        : "15 day free trial and then"}
                    </p>
                  </div>

                  {/* Price */}
                  <div className="text-center mt-4">
                    <p
                      className={`text-3xl font-semibold ${
                        index === 1 ? "text-white" : "text-primary"
                      }`}
                    >
                      {item.currency === "INR" ? "₹" : "$"}
                      {item.price}/{item.isMonthly ? "month" : "year"}
                    </p>
                    <p
                      className={`text-sm font-bold ${
                        index === 1 ? "text-white" : "text-primary"
                      }`}
                    >
                      (incl. GST)
                    </p>
                  </div>

                  {/* Action Button */}
                  <div className="mt-6 flex justify-center">
                    <Button
                      type="button"
                      variant={index === 1 ? "filled" : "lightGreen"}
                      className="!text-base_18 w-full"
                      onClick={() => handleSubscriptionClick(item)}
                      disabled={
                        isCurrentPlan ||
                        isJustPaid ||
                        isMaxValidity ||
                        (isMonthlyPlan && isAnnualActive) ||
                        (hasStartedTrial && isMonthlyPlan)
                      }
                    >
                      {isCurrentPlan
                        ? "Current Plan"
                        : isTrialIncluded && !hasStartedTrial
                        ? "Start Free Trial"
                        : "Pay Now"}
                    </Button>
                  </div>

                  {/* Features */}
                  <div className="mt-6 space-y-4">
                    {subscriptionPlan.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className={`p-3 rounded-lg ${
                          featureIndex % 2 === 0 ? "bg-green-600/10" : ""
                        }`}
                      >
                        <h4
                          className={`font-medium ${
                            index === 1 ? "text-white" : "text-primary"
                          }`}
                        >
                          {feature.title}
                        </h4>
                        <p
                          className={`text-sm mt-1 ${
                            index === 1 ? "text-white/80" : "text-primary/80"
                          }`}
                        >
                          {feature.features}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              );
            }
          )}
        </div>

        <div className="my-2 ">
          {subscriptionDetails?.validDate && (
            <span>
              Your subscription is valid till{" "}
              {MomentHelper.getDateFromIST(
                subscriptionDetails?.validDate as string
              )}
            </span>
          )}
        </div>

        <h2 className="text-xl_30 font-semibold text-primary pt-7.5">
          Payment history
        </h2>

        {/* Desktop View - Hidden on mobile */}
        <div className="hidden lg:block mt-3 w-full border border-primary/10 rounded-base overflow-x-auto">
          <table className="w-full">
            <thead className="text-left">
              <tr className="bg-green-600/10 text-sm/5  text-primary uppercase">
                <th className="px-2.5 py-5 font-medium whitespace-nowrap">#</th>
                <th className="px-2.5 py-5 font-medium whitespace-nowrap">
                  plan
                </th>
                <th className="px-2.5 py-5 font-medium whitespace-nowrap">
                  Amount
                </th>
                <th className="px-2.5 py-5 font-medium whitespace-nowrap">
                  Status
                </th>
                <th className="px-2.5 py-5 font-medium whitespace-nowrap">
                  validity
                </th>
                <th className="px-2.5 py-5 font-medium whitespace-nowrap w-[245px]">
                  subscription date
                </th>
                <th className="px-2.5 py-5 font-medium whitespace-nowrap w-[212px]">
                  purchase date
                </th>
              </tr>
            </thead>
            <tbody>
              {paymentHistoryLoading ? (
                [...Array(5)].map((_, index) => (
                  <tr key={index} className="animate-pulse">
                    <td className="px-2.5 py-3">
                      <div className="h-4 bg-gray-200 rounded"></div>
                    </td>
                    <td className="px-2.5 py-3">
                      <div className="h-4 bg-gray-200 rounded"></div>
                    </td>
                    <td className="px-2.5 py-3">
                      <div className="h-4 bg-gray-200 rounded"></div>
                    </td>
                    <td className="px-2.5 py-3">
                      <div className="h-4 bg-gray-200 rounded"></div>
                    </td>
                    <td className="px-2.5 py-3">
                      <div className="h-4 bg-gray-200 rounded"></div>
                    </td>
                    <td className="px-2.5 py-3">
                      <div className="h-4 bg-gray-200 rounded"></div>
                    </td>
                    <td className="px-2.5 py-3">
                      <div className="h-4 bg-gray-200 rounded"></div>
                    </td>
                  </tr>
                ))
              ) : paymentHistoryData.length === 0 ? (
                // Render "No data found" message when no data is available
                <tr>
                  <td colSpan={7} className="px-2.5 py-3 text-center">
                    <div className="flex flex-col items-center justify-center py-16">
                      <Image
                        width={1000}
                        height={1000}
                        src="/assets/images/dashboard/not-found-payment.webp"
                        alt="no-data"
                        className="w-[172px] h-auto -translate-x-2"
                      />
                      <p className="text-xl/6 font-bold pt-4 text-primary">
                        No Payment History
                      </p>
                    </div>
                  </td>
                </tr>
              ) : (
                // Render table rows when data is available
                (paymentHistoryData as PaymentHistoryItem[])?.map(
                  (item, index) => (
                    <tr key={index} className="text-sm/5 text-primary">
                      <td className="px-2.5 py-3 whitespace-nowrap">
                        {index + 1}
                      </td>
                      <td className="px-2.5 py-3 whitespace-nowrap">
                        {item?.subscriptionId?.name}
                      </td>
                      <td className="px-2.5 py-3 whitespace-nowrap">
                        {item?.subscriptionId ? item?.subscriptionId?.price : 0}
                      </td>
                      <td className="px-2.5 py-3 whitespace-nowrap">
                        <p
                          className={`py-1.5 px-3 inline-block ${
                            item?.subscriptionId?.status === "ACTIVE"
                              ? "text-green-500 bg-green-200 rounded-full"
                              : "text-red-500 bg-red-200 rounded-full"
                          } rounded-full`}
                        >
                          {item?.subscriptionId?.status === "ACTIVE"
                            ? "Active"
                            : "Inactive"}
                        </p>
                      </td>
                      <td className="px-2.5 py-3 whitespace-nowrap">
                        {item?.subscriptionId?.validDays} Days
                      </td>
                      <td className="px-2.5 py-3 whitespace-nowrap">
                        {formatDate(item.validFrom)} to{" "}
                        {formatDate(item.validTill)}
                      </td>
                      <td className="px-2.5 py-3 whitespace-nowrap">
                        {formatDate(item.createdAt)}
                      </td>
                    </tr>
                  )
                )
              )}
            </tbody>
          </table>
        </div>
        {/* Mobile View - Hidden on desktop */}
        <div className="block lg:hidden mt-5">
          {paymentHistoryLoading ? (
            <div className="text-center py-4">Loading...</div>
          ) : paymentHistoryData?.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8">
              <Image
                width={1000}
                height={1000}
                src="/assets/images/dashboard/not-found-payment.webp"
                alt="no-data"
                className="w-[140px] h-auto"
              />
              <p className="text-lg font-bold pt-4 text-primary">
                No Payment History
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {paymentHistoryData?.map((item, index) => (
                <div 
                  key={index}
                  className="bg-white rounded-lg p-4 border border-primary/10"
                >
                  <div className="flex justify-between items-center mb-3">
                    <span className="font-semibold text-primary">
                      {item.subscriptionId?.name}
                    </span>
                    <span className={`text-sm font-medium ${
                      item.subscriptionId?.status?.toLowerCase() === "active"
                        ? "text-green-600"
                        : "text-red-500"
                    }`}>
                      {item.subscriptionId?.status?.toLowerCase() === "active" ? "Active" : "Inactive"}
                    </span>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Amount</span>
                      <span className="font-medium">₹{item.subscriptionId?.price}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Validity</span>
                      <span>{item.subscriptionId?.validDays} days</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">From</span>
                      <span>{formatDate(item.validFrom)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">To</span>
                      <span>{formatDate(item.validTill)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Purchase Date</span>
                      <span>{formatDate(item.createdAt)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        {showSubscriptionInfoModal && (
          <SubscriptionInfoModal
            visible={showSubscriptionInfoModal}
            onClose={handleSubscriptionInfoModalClose}
            onSuccess={() => {
              refetchTherapistData?.();
              setShowSubscriptionInfoModal(false);
            }}
            onSubmitted={handleFormSubmitted}
          />
        )}

        {showConfirmModal && modalPlan && (
          <div className="fixed inset-0 z-[999] bg-black/40 flex items-center justify-center">
            <div className="bg-white rounded-lg p-6 w-[90%] max-w-md shadow-xl">
              <h2 className="text-xl font-semibold text-primary mb-4">
                Confirm Your Subscription
              </h2>

              <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex justify-between py-2">
                  <span className="text-sm font-medium text-gray-600">
                    Plan
                  </span>
                  <span className="text-sm font-semibold text-primary">
                    {modalPlan.name?.split("+")[1]?.trim() || modalPlan.name}
                  </span>
                </div>

                {(() => {
                  const total = modalPlan.price;
                  const basePrice = Math.floor(total - Math.floor(total * 0.2));
                  const gstAmount = Math.floor(total * 0.18);
                  const razorpayFee = Math.floor(total * 0.02);
                  const cgst = Math.floor(total * 0.09);
                  const sgst = Math.floor(total * 0.09);

                  return (
                    <>
                      <div className="flex justify-between py-2 border-t border-gray-200">
                        <span className="text-sm font-medium text-gray-600">
                          Price
                        </span>
                        <span className="text-sm font-semibold text-primary">
                          ₹{basePrice}
                        </span>
                      </div>

                      {therapistData?.address?.state === "Delhi" ? (
                        <div className="flex justify-between py-2 border-t border-gray-200">
                          <span className="text-sm font-medium text-gray-600">
                            IGST (18%)
                          </span>
                          <span className="text-sm font-semibold text-orange-600">
                            ₹{gstAmount}
                          </span>
                        </div>
                      ) : (
                        <>
                          <div className="flex justify-between py-2 border-t border-gray-200">
                            <span className="text-sm font-medium text-gray-600">
                              CGST (9%)
                            </span>
                            <span className="text-sm font-semibold text-orange-600">
                              ₹{cgst}
                            </span>
                          </div>
                          <div className="flex justify-between py-2 border-t border-gray-200">
                            <span className="text-sm font-medium text-gray-600">
                              SGST (9%)
                            </span>
                            <span className="text-sm font-semibold text-orange-600">
                              ₹{sgst}
                            </span>
                          </div>
                        </>
                      )}

                      <div className="flex justify-between py-2 border-t border-gray-300">
                        <span className="text-sm font-medium text-gray-600">
                          Razorpay Fee (2%)
                        </span>
                        <span className="text-sm font-semibold text-orange-600">
                          ₹{razorpayFee}
                        </span>
                      </div>

                      <div className="flex justify-between py-2 border-t border-gray-300">
                        <span className="text-base font-semibold text-gray-800">
                          Total
                        </span>
                        <span className="text-base font-bold text-green-600">
                          ₹{total}
                        </span>
                      </div>
                    </>
                  );
                })()}
              </div>

              <div className="flex justify-end gap-3">
                <Button
                  variant="light"
                  onClick={() => setShowConfirmModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    createPayment({
                      subscriptionId: modalPlan._id || "",
                      subscriptionName: modalPlan.name || "",
                    });
                    setShowConfirmModal(false);
                  }}
                >
                  Confirm & Pay
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </SettingLayout>
  );
};

export default Subscription;
