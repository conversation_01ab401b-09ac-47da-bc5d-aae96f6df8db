/**
 * Utility functions for image processing and optimization
 */

/**
 * Generate a compressed image URL for social media link previews
 * @param originalImageUrl - The original image URL
 * @param maxSizeKB - Maximum file size in KB (default: 300KB)
 * @param compressionDomain - Domain to use for compression endpoint (optional)
 * @returns Compressed image URL
 */
export function getCompressedImageUrl(originalImageUrl: string, maxSizeKB: number = 300, compressionDomain?: string): string {
  // If no image URL provided, return empty string
  if (!originalImageUrl || originalImageUrl === 'null' || originalImageUrl === 'undefined') {
    return '';
  }

  // If it's already a local fallback image, return as is
  if (originalImageUrl.startsWith('/assets/')) {
    return originalImageUrl;
  }

  // Get base URL for the compression endpoint
  const baseUrl = compressionDomain || process.env.NEXT_PUBLIC_BASE_URL || 'https://app.thoughtpudding.com';

  // Create compressed image URL
  const compressedUrl = `${baseUrl}/api/image-compress?url=${encodeURIComponent(originalImageUrl)}&maxSize=${maxSizeKB * 1000}`;

  return compressedUrl;
}

/**
 * Get optimized image URL for social media previews
 * Ensures the image is under 300KB for better link preview compatibility
 * @param profileImageUrl - The profile image URL from the API
 * @param fallbackImage - Fallback image path if no profile image (default: empty string)
 * @param compressionDomain - Domain to use for compression endpoint (optional)
 * @returns Optimized image URL for social media
 */
export function getSocialMediaImageUrl(
  profileImageUrl: string | Record<string, unknown> | null | undefined,
  fallbackImage: string = '',
  compressionDomain?: string
): string {
  // Helper function to safely extract string values
  const safeString = (value: string | Record<string, unknown> | null | undefined): string => {
    if (typeof value === 'string') return value;
    return '';
  };

  let imageUrl = safeString(profileImageUrl);

  // If no profile image or it's not a valid URL, use fallback or log
  if (!imageUrl || imageUrl === 'null' || imageUrl === 'undefined') {
    return fallbackImage;
  }

  // If it's a relative path, make it absolute
  if (!imageUrl.startsWith('http')) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://app.thoughtpudding.com';
    imageUrl = imageUrl.startsWith('/') ? `${baseUrl}${imageUrl}` : `${baseUrl}/${imageUrl}`;
  }

  // Return compressed version for social media
  return getCompressedImageUrl(imageUrl, 300, compressionDomain);
}

/**
 * Check if an image URL is likely to be large and needs compression
 * @param imageUrl - The image URL to check
 * @returns boolean indicating if compression is recommended
 */
export function shouldCompressImage(imageUrl: string): boolean {
  // Always compress S3 images as they tend to be large
  if (imageUrl.includes('s3.') || imageUrl.includes('amazonaws.com')) {
    return true;
  }

  // Compress images from external domains
  if (imageUrl.startsWith('http') && !imageUrl.includes('thoughtpudding.com')) {
    return true;
  }

  // Don't compress local assets
  if (imageUrl.startsWith('/assets/')) {
    return false;
  }

  // Default to compression for safety
  return true;
}

/**
 * Get the appropriate image URL based on usage context
 * @param profileImageUrl - The profile image URL from the API
 * @param context - The context where the image will be used
 * @param fallbackImage - Fallback image path (default: empty string)
 * @returns Appropriate image URL for the context
 */
export function getContextualImageUrl(
  profileImageUrl: string | Record<string, unknown> | null | undefined,
  context: 'social-media' | 'display' | 'thumbnail',
  fallbackImage: string = ''
): string {
  const safeString = (value: string | Record<string, unknown> | null | undefined): string => {
    if (typeof value === 'string') return value;
    return '';
  };

  let imageUrl = safeString(profileImageUrl);

  // If no profile image or it's not a valid URL, use fallback or log
  if (!imageUrl || imageUrl === 'null' || imageUrl === 'undefined') {
    return fallbackImage;
  }

  // If it's a relative path, make it absolute
  if (!imageUrl.startsWith('http')) {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://app.thoughtpudding.com';
    imageUrl = imageUrl.startsWith('/') ? `${baseUrl}${imageUrl}` : `${baseUrl}/${imageUrl}`;
  }

  // Return appropriate URL based on context
  switch (context) {
    case 'social-media':
      return getCompressedImageUrl(imageUrl, 300); // 300KB limit for social media
    case 'thumbnail':
      return getCompressedImageUrl(imageUrl, 100); // 100KB limit for thumbnails
    case 'display':
    default:
      // For display purposes, only compress if it's likely to be large
      return shouldCompressImage(imageUrl) ? getCompressedImageUrl(imageUrl, 500) : imageUrl;
  }
}
