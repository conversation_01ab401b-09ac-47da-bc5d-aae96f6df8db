import axiosInstance from "@/utils/axios";
import { AuthService } from "@/services/auth.service";
import { useRouter } from "next/navigation";

export const checkTherapistVerification = async (
  setIsAuthenticated: (auth: boolean) => void,
  router: ReturnType<typeof useRouter>,
  th_id: string
) => {
  await AuthService.getTherapistVerificationData(th_id)
    .then((res) => {
      const isVerified = res.data.therapist.isVerified;

      if (res.status === 200 && isVerified) {
        //if verified user use tempAuth<PERSON><PERSON> to authorize and set authKeyTh and authenticate his login.
        if (
          typeof window !== "undefined" &&
          typeof sessionStorage !== "undefined" &&
          sessionStorage.getItem("tempAuthKey")
        ) {
          const token = sessionStorage.getItem("tempAuthKey") ?? "";
          if (token) {
            axiosInstance.defaults.headers.Authorization = token;
            localStorage.setItem("authKeyTh", token);
            setIsAuthenticated(true);
          }
        }
      }
    })
    .catch((e) => {
      //if error redirected to google login again.Can happen if new tab opened so no tempAuthKey in sessionStorage.
      console.error(e, "error while checking therapist verification.");
      router.push("/google-signin");
    });
};
