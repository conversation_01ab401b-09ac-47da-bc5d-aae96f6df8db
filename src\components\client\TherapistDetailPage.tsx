'use client';

import { Caveat } from "next/font/google";
import { TherapistData } from "../../app/clients/Landing Page/types/therapist";
import { useState, useEffect } from "react";

const caveat = Caveat({ subsets: ["latin"], weight: "400" });

interface TherapistDetailPageProps {
  therapist: TherapistData;
}

export default function TherapistDetailPage({ therapist }: TherapistDetailPageProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('header')) {
        setMobileMenuOpen(false);
      }
    };

    if (mobileMenuOpen) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [mobileMenuOpen]);

  const handleBookSession = () => {
    // Open email with predefined structure
    const emailBody = `Hi Thought Pudding Team,

My name is [Your Name], and I'm looking to take the first step towards starting therapy.

I'm looking for: [Individual Therapy / Couples Therapy / Not Sure]

What brings me to therapy: [A sentence or two— optional]

Preferred mode: [In-person at Powai, Mumbai/ Online]

Looking forward to untangling my thoughts with you!

Warm regards,
[Your Name]`;
    
    const emailUrl = `mailto:<EMAIL>?subject=Ready to take the first step, looking for therapy&body=${encodeURIComponent(emailBody)}`;
    window.location.href = emailUrl;
  };

  const scrollToSection = (sectionId: string) => {
    // Navigate to main page with section
    window.location.href = `/clients#${sectionId}`;
    setMobileMenuOpen(false);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header Navigation */}
      <header className="bg-white border-b relative">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <nav className="flex items-center justify-between">
            {/* Logo */}
            <div className="w-32 h-13 md:w-48 md:h-20">
              <img 
                src="/assets/images/client/client-logo.svg" 
                alt="Thought Pudding" 
                className="w-full h-full object-contain"
              />
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center gap-8">
              <a href="/clients#about" className="text-black font-medium hover:text-gray-600">About Us</a>
              <a href="/clients#services" className="text-black font-medium hover:text-gray-600">Services</a>
              <div className="bg-[#E9FA6F] rounded-t-xl px-6 py-3">
                <a href="/clients#about" className="text-black font-medium">Meet our Therapists</a>
              </div>
            </div>

            {/* Mobile Hamburger Menu */}
            <div className="lg:hidden">
              <button
                className="flex flex-col justify-center items-center w-12 h-12 focus:outline-none hover:bg-gray-100 rounded-lg transition-colors"
                aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
                onClick={(e) => {
                  e.stopPropagation();
                  setMobileMenuOpen(!mobileMenuOpen);
                }}
              >
                <span className={`block w-7 h-0.5 bg-black mb-1.5 rounded transition-all duration-200 ${
                  mobileMenuOpen ? 'rotate-45 translate-y-2' : ''
                }`} />
                <span className={`block w-7 h-0.5 bg-black mb-1.5 rounded transition-all duration-200 ${
                  mobileMenuOpen ? 'opacity-0' : ''
                }`} />
                <span className={`block w-7 h-0.5 bg-black rounded transition-all duration-200 ${
                  mobileMenuOpen ? '-rotate-45 -translate-y-2' : ''
                }`} />
              </button>
            </div>
          </nav>

          {/* Mobile Dropdown Menu */}
          {mobileMenuOpen && (
            <div className="lg:hidden absolute top-full right-0 left-0 bg-white shadow-lg z-50 border-t">
              <nav className="flex flex-col items-center py-4 gap-2">
                <button
                  onClick={() => {
                    scrollToSection('why-different');
                    setMobileMenuOpen(false);
                  }}
                  className="w-full text-base font-medium text-black hover:text-[#718FFF] transition-colors duration-200 py-2"
                >
                  About Us
                </button>
                <button
                  onClick={() => {
                    scrollToSection('services');
                    setMobileMenuOpen(false);
                  }}
                  className="w-full text-base font-medium text-black hover:text-[#718FFF] transition-colors duration-200 py-2"
                >
                  Services
                </button>
                <button
                  onClick={() => {
                    scrollToSection('about');
                    setMobileMenuOpen(false);
                  }}
                  className={`w-full px-5 py-4 bg-[#E9FA6F] text-black rounded-[10px] font-bold hover:opacity-90 transition-all duration-200 shadow-sm hover:shadow-md mt-2 ${caveat.className}`}
                  style={{ fontSize: '18px' }}
                >
                  Meet Our Therapists
                </button>
              </nav>
            </div>
          )}
        </div>
        <hr className="border-black" />
      </header>

      <main className="max-w-7xl mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-8">
          <button 
            onClick={() => window.history.back()}
            className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded-lg transition-colors"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M19 12H5M12 19L5 12L12 5" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 min-h-[600px]">
          {/* Left Column - Therapist Photo */}
          <div className="lg:col-span-1">
            <div className="relative w-full h-full min-h-[600px]">
              <div className="absolute inset-0 rounded-2xl overflow-hidden">
                <img 
                  src={therapist.image}
                  alt={therapist.name}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Top Row - Main Info and Languages */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Main Info Card */}
              <div className="lg:col-span-3 bg-[#DACBFF] rounded-2xl p-6">
                <div className="mb-6">
                  <h1 className={`text-4xl font-bold text-[#252525] mb-2 ${caveat.className} font-bold`}>
                    {therapist.name} {therapist.pronouns && `(${therapist.pronouns})`}
                  </h1>
                  <p className="text-[#252525] font-medium mb-4">{therapist.title}</p>
                  
                  <div className="flex gap-4">
                    <div className="bg-white px-4 py-2 rounded-lg">
                      <span className="text-[#252525] font-medium">{therapist.experience}</span>
                    </div>
                    <div className="bg-[#718FFF] px-4 py-2 rounded-lg">
                      <span className="text-[#252525] font-medium">
                        {therapist.sessionFormat}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Therapy Description */}
                <div>
                  <h3 className={`text-2xl font-semibold text-[#252525] mb-3 ${caveat.className} font-bold`}>
                    What therapy looks like with me
                  </h3>
                  <p className="text-[#252525] leading-relaxed">
                    {therapist.therapyDescription}
                  </p>
                </div>
              </div>

              {/* Languages Card - Separate */}
              <div className="lg:col-span-1 bg-[#FFCFE3] rounded-2xl p-6">
                <h3 className={`text-3xl text-[#252525] mb-4 ${caveat.className} font-bold`}>
                  Languages
                </h3>
                <div className="space-y-2">
                  {therapist.languages.map((language, index) => (
                    <div key={index} className="bg-white px-3 py-1 rounded-lg text-center">
                      <span className="text-[#252525] font-medium">{language}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Three Column Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Values */}
              <div className="bg-[#DACBFF] rounded-2xl p-6">
                <h3 className={`text-2xl text-[#252525] mb-4 ${caveat.className} font-bold`}>
                  Values I Align With as a Therapist
                </h3>
                <div className="space-y-3">
                  {therapist.values.map((value, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-[#252525] rounded-full flex-shrink-0"></div>
                      <span className="text-[#252525] text-sm">{value}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Types of Therapy */}
              <div className="bg-[#FFCFE3] rounded-2xl p-6">
                <h3 className={`text-2xl text-[#252525] mb-4 ${caveat.className} font-bold`}>
                  Types of Therapy
                </h3>
                <div className="space-y-3">
                  {therapist.therapyTypes.map((type, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-[#252525] rounded-full flex-shrink-0"></div>
                      <span className="text-[#252525] text-sm">{type}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Concerns */}
              <div className="bg-[#DACBFF] rounded-2xl p-6">
                <h3 className={`text-2xl text-[#252525] mb-4 ${caveat.className} font-bold`}>
                  Concerns I work with
                </h3>
                <div className="space-y-3">
                  {therapist.concerns.map((concern, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-[#252525] rounded-full flex-shrink-0"></div>
                      <span className="text-[#252525] text-sm">{concern}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Qualifications - Full Width */}
            <div className="bg-[#DACBFF] rounded-2xl p-6">
              <h3 className={`text-2xl text-[#252525] mb-4 ${caveat.className} font-bold`}>
                Qualification & Training:
              </h3>
              <p className="text-[#252525]">
                {therapist.qualifications.join(', ')}
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* Bottom Book Session Bar */}
      <div className="mt-8 mb-8">
        <div className="max-w-7xl mx-auto px-4">
          {/* Desktop Layout */}
          <div className="hidden md:block">
            <div className="bg-[#6E58A5] rounded-[60px] px-5 py-5 flex justify-center items-center shadow-lg max-w-[1258px] mx-auto">
              {/* Book Session Button */}
              <button 
                onClick={handleBookSession}
                className="bg-[#E9FA6F] rounded-[40px] px-5 py-3 flex items-center gap-12 hover:bg-[#d4e559] transition-colors"
              >
                <span className={`text-black font-medium text-2xl ${caveat.className} font-bold`}>Book Session</span>
                <div className="w-10 h-10 bg-black rounded-full flex items-center justify-center">
                  <svg width="17" height="1" viewBox="0 0 17 1" fill="none">
                    <path d="M0 0.5L17 0.5" stroke="white" strokeWidth="2.18"/>
                  </svg>
                </div>
              </button>
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="md:hidden">
            {/* Book Session Button - Mobile */}
            <button 
              onClick={handleBookSession}
              className="w-full bg-[#E9FA6F] rounded-2xl px-4 py-4 flex items-center justify-center gap-4 hover:bg-[#d4e559] transition-colors"
            >
              <span className={`text-black font-medium text-xl ${caveat.className} font-bold`}>Book Session</span>
              <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center">
                <svg width="14" height="1" viewBox="0 0 17 1" fill="none">
                  <path d="M0 0.5L17 0.5" stroke="white" strokeWidth="2.18"/>
                </svg>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-end gap-5">
            <span className="text-black font-semibold">Powered by</span>
            <div className="w-52 h-8">
              <img 
                src="/assets/images/client/client-logo.svg" 
                alt="Thought Pudding" 
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
} 