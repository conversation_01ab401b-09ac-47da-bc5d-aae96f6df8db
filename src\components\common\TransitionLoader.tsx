"use client";
import React, { useEffect, useState } from 'react';

interface TransitionLoaderProps {
  isVisible: boolean;
  message: string;
  redirectMessage?: string;
  progress?: number; // 0 to 100
}

const TransitionLoader: React.FC<TransitionLoaderProps> = ({
  isVisible,
  message,
  redirectMessage = "Redirecting...",
  progress = 0
}) => {
  const [mounted, setMounted] = useState(false);

  // Animation effect when component mounts
  useEffect(() => {
    if (isVisible) {
      setMounted(true);
    } else {
      const timer = setTimeout(() => setMounted(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  if (!isVisible && !mounted) return null;

  return (
    <div
      className={`fixed inset-0 bg-white z-50 flex flex-col items-center justify-center transition-all duration-500 ease-in-out ${
        mounted ? 'opacity-95 backdrop-blur-sm' : 'opacity-0 backdrop-blur-none'
      }`}
    >
      <div className={`w-80 flex flex-col items-center transition-all duration-500 transform ${
        mounted ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
      }`}>
        {/* Modern pulse animation */}
        <div className="relative mb-8">
          <div className="absolute inset-0 rounded-full bg-blue-500 opacity-30 animate-ping"></div>
          <div className="relative flex items-center justify-center w-20 h-20 rounded-full bg-blue-600">
            {/* Animated checkmark that appears based on progress */}
            {progress > 95 ? (
              <svg
                className="w-10 h-10 text-white animate-scale-in"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="3"
                  d="M5 13l4 4L19 7"
                  className="animate-draw-check"
                ></path>
              </svg>
            ) : (
              <div className="w-12 h-12">
                {/* Animated dots */}
                <div className="flex space-x-1 justify-center items-center h-full">
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce-delay-1"></div>
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce-delay-2"></div>
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce-delay-3"></div>
                </div>
              </div>
            )}
          </div>
        </div>

        <h3 className="text-xl w-full font-medium text-blue-700 mb-2 transition-all duration-300 ease-in-out text-center whitespace-nowrap overflow-hidden text-ellipsis">{message}</h3>
        <p className="text-gray-600 text-center mb-6">{redirectMessage}</p>

        {/* Modern progress bar with gradient and animation */}
        <div className="w-full bg-gray-100 rounded-full h-2 overflow-hidden">
          <div
            className="h-full rounded-full transition-all duration-300 ease-out bg-gradient-to-r from-blue-500 to-blue-600"
            style={{
              width: `${progress}%`,
              boxShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
            }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default TransitionLoader;
