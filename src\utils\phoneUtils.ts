/**
 * Utility functions for phone number processing
 */

/**
 * Extracts 10-digit mobile number from various phone number formats
 * Removes country codes and returns only the 10-digit number
 * @param phoneNumber - Phone number in various formats (with/without country code)
 * @returns 10-digit mobile number or null if invalid
 */
export const extractTenDigitMobile = (phoneNumber: string | undefined): string | null => {
  if (!phoneNumber) return null;

  // Remove all non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, '');

  // If it's exactly 10 digits, return as is
  if (digitsOnly.length === 10) {
    return digitsOnly;
  }

  // If it starts with 91 and has more than 10 digits, extract the part after 91
  if (digitsOnly.startsWith('91') && digitsOnly.length > 10) {
    const withoutCountryCode = digitsOnly.substring(2);
    if (withoutCountryCode.length === 10) {
      return withoutCountryCode;
    }
  }

  // If it's more than 10 digits, try to extract the last 10
  if (digitsOnly.length > 10) {
    const lastTen = digitsOnly.substring(digitsOnly.length - 10);
    // Just check if it's 10 digits
    if (/^\d{10}$/.test(lastTen)) {
      return lastTen;
    }
  }

  return null;
};

/**
 * Validates if a phone number is a valid 10-digit mobile number
 * @param phoneNumber - Phone number to validate
 * @returns true if valid, false otherwise
 */
export const validateIndianMobile = (phoneNumber: string | undefined): boolean => {
  if (!phoneNumber) return false;

  const tenDigitNumber = extractTenDigitMobile(phoneNumber);
  if (!tenDigitNumber) return false;

  // Just check if it's exactly 10 digits
  return /^\d{10}$/.test(tenDigitNumber);
};

/**
 * Formats a phone number for display (adds +91 prefix)
 * @param phoneNumber - 10-digit mobile number
 * @returns formatted phone number with +91 prefix
 */
export const formatPhoneForDisplay = (phoneNumber: string): string => {
  const tenDigitNumber = extractTenDigitMobile(phoneNumber);
  if (!tenDigitNumber) return phoneNumber;
  
  return `+91${tenDigitNumber}`;
};

/**
 * Gets error message for invalid phone numbers
 * @param phoneNumber - Phone number to validate
 * @returns error message or null if valid
 */
export const getPhoneValidationError = (phoneNumber: string | undefined): string | null => {
  if (!phoneNumber) {
    return "Mobile number is required";
  }
  
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  
  if (digitsOnly.length === 0) {
    return "Mobile number is required";
  }
  
  if (digitsOnly.length < 10) {
    return "Mobile number must be at least 10 digits";
  }
  
  const tenDigitNumber = extractTenDigitMobile(phoneNumber);
  if (!tenDigitNumber) {
    return "Invalid mobile number format";
  }
  
  if (!/^\d{10}$/.test(tenDigitNumber)) {
    return "Invalid mobile number format";
  }
  
  return null;
};
