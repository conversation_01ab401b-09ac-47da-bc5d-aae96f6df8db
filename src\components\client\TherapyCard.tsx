'use client'

import Image from 'next/image'
import BeginTherapyButton from './BeginTherapyButton'
import { useState, useEffect } from 'react'

interface TherapyCardProps {
  className?: string
  title: string
  cardTitle: string
  cardDescription: string
  tagText: string
  svgPath: string
  svgSize?: { width: number; height: number }
  svgPosition?: { top: number; left: number }
  innerCardSize?: { width: number; height: number }
  outerBgColor: string
  innerBgColor: string
}

export default function TherapyCard({ 
  className = '',
  title,
  cardTitle,
  cardDescription,
  tagText,
  svgPath,
  svgSize = { width: 240, height: 240 },
  svgPosition = { top: 90, left: 20 },
  innerCardSize = { width: 280, height: 208 },
  outerBgColor,
  innerBgColor
}: TherapyCardProps) {
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsDesktop(window.innerWidth >= 1500);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);
  return (
    <div className={`relative w-full h-[570px] ${className}`} style={{
      maxWidth: isDesktop ? '580px' : '400px'
    }}>
      {/* Outer Rectangle - Configurable Background */}
      <div className={`absolute inset-0 ${outerBgColor} rounded-2xl`}>
        
        {/* Main Title */}
        <div className="absolute top-[40px] left-1/2 transform -translate-x-1/2 z-10">
          <h2
            className="text-white font-bold text-center whitespace-nowrap font-new-elegance italic"
            style={{ fontSize: isDesktop ? '32px' : '20px' }}
          >
            {title}
          </h2>
        </div>
        
                {/* Animated Background Image */}
        {isDesktop && (
          <div 
            className="absolute"
            style={{ 
              top: `${svgPosition.top}px`,
              left: `${svgPosition.left}px`,
              width: `${svgSize.width}px`, 
              height: `${svgSize.height}px`
            }}
          >
            <Image
              src={svgPath}
              alt="Therapy illustration"
              fill
              className="object-contain"
              priority
            />
          </div>
        )}

                {/* Mobile SVG - Centered and repositioned */}
        {!isDesktop && (
          <div className="absolute top-[80px] left-1/2 transform -translate-x-1/2 w-[200px] h-[120px]">
            <Image
              src={svgPath}
              alt="Therapy illustration"
              fill
              className="object-contain"
              priority
            />
          </div>
        )}
        
        {/* Inner Card - Desktop */}
        {isDesktop && (
          <div 
            className={`absolute top-[120px] right-[20px] ${innerBgColor} rounded-[8px] p-0 flex flex-col`}
            style={{ 
              width: `${innerCardSize.width}px`,
              height: `${innerCardSize.height}px`
            }}
          >
            {/* Card Content */}
            <div className="p-6 flex-1">
              <h3 className="text-white text-[18px] font-bold mb-3 leading-tight">
                {cardTitle}
              </h3>
              
              {/* Card Description */}
              <p 
                className="text-white text-[14px] font-medium opacity-90 leading-relaxed"
                style={{ whiteSpace: 'pre-line' }}
              >
                {cardDescription}
              </p>
            </div>
            
            {/* Tag - Full width, no margins */}
            <div className="bg-[#FDF3DD] h-[40px] flex items-center justify-center px-3 rounded-b-[8px]">
              <span className={`text-[12px] font-bold ${innerBgColor.includes('#5A4689') ? 'text-[#5A4689]' : 'text-gray-800'}`}>
                {tagText}
              </span>
            </div>
          </div>
        )}

        {/* Inner Card - Mobile */}
        {!isDesktop && (
          <div 
            className={`absolute top-[220px] left-[20px] right-[20px] ${innerBgColor} rounded-[8px] p-0 flex flex-col`}
            style={{ minHeight: '160px' }}
          >
          {/* Card Content */}
          <div className="p-5 flex-1">
            <h3 className="text-white text-[16px] font-bold mb-3 leading-tight">
              {cardTitle}
            </h3>
            
            {/* Card Description */}
            <p 
              className="text-white text-[13px] font-medium opacity-90 leading-relaxed"
              style={{ whiteSpace: 'pre-line' }}
            >
              {cardDescription}
            </p>
          </div>
          
            {/* Tag - Full width, no margins */}
            <div className="bg-[#FDF3DD] h-[40px] flex items-center justify-center px-3 rounded-b-[8px]">
              <span className={`text-[12px] font-bold ${innerBgColor.includes('#5A4689') ? 'text-[#5A4689]' : 'text-gray-800'}`}>
                {tagText}
              </span>
            </div>
          </div>
        )}
        
        {/* White Divider Line */}
        <div className="absolute bottom-[60px] left-[20px] right-[20px] h-[2px] bg-white"></div>
        
        {/* Begin Therapy Button - Overlapping the divider */}
        <div className="absolute bottom-[35px] right-[20px] z-20">
          <BeginTherapyButton className="!text-[14px] !px-7 !py-4 !w-[213px]" />
        </div>
      </div>
    </div>
  )
} 