/* Import <PERSON><PERSON> and <PERSON><PERSON> fonts */
@font-face {
  font-family: 'Gilmer-Bold';
  src: url('/fonts/Gilmer-Bold.woff2') format('woff2'),
       url('/fonts/Gilmer-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilmer-Medium';
  src: url('/fonts/Gilmer-Medium.woff2') format('woff2'),
       url('/fonts/Gilmer-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilmer-Regular';
  src: url('/fonts/Gilmer-Regular.woff2') format('woff2'),
       url('/fonts/Gilmer-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* Font classes */
.gilmer-bold {
  font-family: 'Gilmer-Bold', sans-serif;
}

.gilmer-medium {
  font-family: 'Gilmer-Medium', sans-serif;
}

.gilmer-regular {
  font-family: 'Gilmer-Regular', sans-serif;
}

.roboto {
  font-family: 'Roboto', sans-serif;
}

.bg-page {
  margin: 0;
  padding: 0;
  background-image: url('/assets/images/newHome/bg-carousal.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-color: #2A1B6D; /* fallback color */
  color: white;
  min-height: 100%;
  width: 100%;
}