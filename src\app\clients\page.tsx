"use client";
import ClientLayout from "@/layout/client/ClientLayout";
import ClientHeroSection from "./Landing Page/HeroSection";
import QuestionBubblesSection from "./Landing Page/QuestionBubblesSection";
import ClientHowItWorksSection from "./Landing Page/HowItWorksSection";
import ClientWhyDifferentSection from "./Landing Page/WhyDifferentSection";
import ClientServicesSection from "./Landing Page/ServicesSection";
import ClientWhoWeAreSection from "./Landing Page/WhoWeAreSection";
import ClientFooter from "./Landing Page/Footer";

export default function ClientLandingPage() {
  return (
    <ClientLayout>
      <div className="min-h-screen bg-white">
        <div className="pt-[120px] lg:px-[150px] lg:py-[100px] md:px-[75px] md:py-[60px] px-4 py-8">
          <ClientHeroSection />
        </div>
        <QuestionBubblesSection />
        <div className="lg:px-[150px] lg:py-[60px] md:px-[75px] md:py-[40px] px-4 py-6">
          <ClientHowItWorksSection />
          <ClientWhoWeAreSection />
          <ClientWhyDifferentSection />
          <ClientServicesSection />
        </div>
        <ClientFooter />
      </div>
    </ClientLayout>
  );
}
