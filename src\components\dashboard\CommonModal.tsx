import { X } from "@phosphor-icons/react";
import React, { useEffect } from "react";

interface CommonModalProps {
  setIsClose: (value: boolean) => void; // Adjust the type as necessary
  className?: string; // Explicitly define className as optional string
  title: string;
  children: React.ReactNode;
  isClose: boolean;
  remove?: unknown;
}

const CommonModal: React.FC<CommonModalProps> = ({
  title,
  children,
  isClose,
  setIsClose,
  className,
  remove,
}) => {
  useEffect(() => {
    document.body.style.overflow = isClose ? "hidden" : "auto";
    return () => {
      document.body.style.overflow = "auto"; // Ensure this returns void
    };
  }, [isClose]);

  return (
    <div
      className={`fixed top-0 left-0 w-full h-full bg-black/20 z-[999] flex items-center justify-center transition-all duration-300 ${
        isClose ? "opacity-100 visible" : "opacity-0 invisible"
      }`}
    >
      <div
        className={`w-full sm:max-w-[578px] max-w-[90%] mx-4 transition-all duration-500 ${className} ${
          isClose ? "opacity-100 visible" : "opacity-0 invisible"
        }`}
      >
        <div className="p-6 bg-white rounded-lg shadow-lg w-full">
          <div className="">
            <div className="flex items-center justify-between">
              <h4 className="sm:text-lg text-base font-semibold text-primary">
                {title}
              </h4>
              <button
                onClick={() => setIsClose(!isClose)}
                className={`${remove ? "hidden" : ""}`}
              >
                <X size={20} />
              </button>
            </div>
            <div className="mt-4">{children}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommonModal;
