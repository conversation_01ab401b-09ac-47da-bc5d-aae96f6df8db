import axiosInstance, { fetcher } from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import { useMemo } from "react";
import toast from "react-hot-toast";
import useSWR from "swr";

const swrOptions = {
  revalidateOnFocus: true, // Revalidate when the component is focused
  revalidateOnReconnect: true, // Revalidate when the connection is re-established
  shouldRetryOnError: true, // Retry on error
  // revalidateIfStale: false,
  dedupingInterval: 0, // No deduplication, always fetch from the API
  refreshInterval: 0,
};

type ScheduleData = {
  clients: Array<unknown>;
  clientCount?: number;
  stats?: unknown;
  totalClients?: number;
};

// get activity data
export function useGetClientCount(startDate: Date, endDate: Date) {
  const query = `startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`;
  const url = `${endpoints.clients.clientCount}?${query}`;

  const { data, isLoading, error, isValidating } = useSWR<ScheduleData>(
    url,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      clientsCountData: data?.stats || [],
      clientsCountLoading: isLoading,
      clientsCountError: error,
      clientsCountValidating: isValidating,
    }),
    [data?.stats, error, isLoading, isValidating]
  );

  return memoizedValue;
}

export interface FilterParams {
  searchStartDate?: string;
  searchEndDate?: string;
  fromPublicCalender?: boolean;
}
// Clints listing
export function useGetClients(
  pageSize: number,
  pageNumber: number,
  activeTable: string,
  debouncedSearchText: string,
  filterparams: FilterParams
) {
  const query =
    `pageSize=${pageSize}&pageNumber=${pageNumber}&isActive=${activeTable}&searchText=${debouncedSearchText}` +
    (filterparams && filterparams.searchStartDate && filterparams.searchEndDate
      ? `&startDate=${filterparams.searchStartDate}&endDate=${filterparams.searchEndDate}`
      : "") +
    (filterparams && filterparams.fromPublicCalender !== undefined
      ? `&fromPublicCalender=${filterparams.fromPublicCalender}`
      : "");
  const url = `${endpoints.clients.clients}?${query}`;

  const { data, isLoading, error, isValidating } = useSWR<ScheduleData>(
    url,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      clientsData: data?.clients || [],
      clientsCount: data?.totalClients || 0,
      clientsLoading: isLoading,
      clientsError: error,
      clientsValidating: isValidating,
    }),
    [data?.clients, data?.totalClients, error, isLoading, isValidating]
  );

  return memoizedValue;
}

export async function getClientById(singleClientById: string) {
  if (!singleClientById) return;
  const query = `${singleClientById}`;
  const url = `${endpoints.clients.getClientById}/${query}`;
  const response = await axiosInstance.get(url);

  return response.data.data;
}

export function useGetTimeZone() {
  const url = `${endpoints.timeZones}`;

  const { data, isLoading } = useSWR(url, fetcher, swrOptions);
  const memoizedValue = useMemo(
    () => ({
      timeZoneData: data?.timezones,
      timeZoneLoading: isLoading,
    }),
    [data?.timezones, isLoading]
  );

  return memoizedValue;
}

export interface UpdatedClientData {
  name: string;
  phone: string;
  age: string;
  gender: string;
  defaultTimezone: string;
  isActive: boolean;
}

export async function putclientById(
  singleClientById: string,
  updatedData: UpdatedClientData,
  toastId: string
) {
  try {
    const url = `${endpoints.clients.putClientById}/${singleClientById}`;
    await axiosInstance.put(url, updatedData);
    toast.dismiss(toastId);
    toast.success("Client updated successfully!");
    return true;
  } catch (error) {
    toast.dismiss(toastId);
    return error;
  }
}
