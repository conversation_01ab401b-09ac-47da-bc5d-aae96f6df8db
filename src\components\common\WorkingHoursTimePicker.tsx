import { Clock } from "@phosphor-icons/react";
import { useState, useEffect, useRef, ChangeEvent } from "react";

interface WorkingHoursTimePickerProps {
  className?: string;
  value?: string;
  onChange?: (value: string) => void;
  isStartTime: boolean;
  startTime?: string; // Required for end time picker to calculate options
  duration?: number; // Duration in minutes (for end time calculation)
}

const WorkingHoursTimePicker: React.FC<WorkingHoursTimePickerProps> = ({
  className,
  value,
  onChange,
  isStartTime,
  startTime,
  duration = 15, // Default to 15 minutes
}) => {
  const [selectedTime, setSelectedTime] = useState(value || "00:00");
  const [showDropdown, setShowDropdown] = useState(false);
  const [inputValue, setInputValue] = useState(value || "00:00");
  const [isValid, setIsValid] = useState(true);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Detect mobile devices
  const isMobile = () => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
           (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) ||
           window.innerWidth < 768;
  };

  // Generate times with appropriate intervals
  const generateTimes = () => {
    if (isStartTime) {
      // For start time, generate times with 15-minute intervals
      const times: string[] = [];
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 15) {
          const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
          const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
          times.push(`${formattedHour}:${formattedMinute}`);
        }
      }
      return times;
    } else if (startTime) {
      // For end time, generate options based on start time
      const times: string[] = [];
      const [startHour, startMinute] = startTime.split(":").map(Number);
      const startTimeInMinutes = startHour * 60 + startMinute;

      // Generate 4 options with 15-minute intervals from the start time
      for (let i = 1; i <= 4; i++) {
        const totalMinutes = startTimeInMinutes + (i * 15);
        const hour = Math.floor(totalMinutes / 60) % 24;
        const minute = totalMinutes % 60;
        const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
        const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
        times.push(`${formattedHour}:${formattedMinute}`);
      }

      // Also include the current end time if it's not in the list
      const currentEndTime = selectedTime;
      if (currentEndTime && !times.includes(currentEndTime)) {
        times.push(currentEndTime);
        // Sort the times
        times.sort((a, b) => {
          const [aHour, aMinute] = a.split(":").map(Number);
          const [bHour, bMinute] = b.split(":").map(Number);
          const aMinutes = aHour * 60 + aMinute;
          const bMinutes = bHour * 60 + bMinute;
          return aMinutes - bMinutes;
        });
      }

      return times;
    }

    return [];
  };

  const times = generateTimes();

  useEffect(() => {
    if (value !== undefined && value !== selectedTime) {
      setSelectedTime(value);
      setInputValue(value);
      setIsValid(true);
    }
  }, [value, selectedTime]);

  // If this is an end time picker, update options when start time or duration changes
  useEffect(() => {
    if (!isStartTime && startTime) {
      // Calculate new end time based on start time and duration
      const [startHour, startMinute] = startTime.split(":").map(Number);
      const startTimeInMinutes = startHour * 60 + startMinute;

      // Calculate end time based on duration
      const totalMinutes = startTimeInMinutes + duration;
      const hour = Math.floor(totalMinutes / 60) % 24;
      const minute = totalMinutes % 60;
      const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
      const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
      const newEndTime = `${formattedHour}:${formattedMinute}`;

      // Only update if the calculated end time is different from the current one
      if (newEndTime !== selectedTime && onChange) {
        setSelectedTime(newEndTime);
        setInputValue(newEndTime);
        onChange(newEndTime);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startTime, isStartTime, duration]);

  const handleSelectTime = (time: string, event?: React.MouseEvent) => {
    // Prevent event bubbling
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Update state immediately
    setSelectedTime(time);
    setInputValue(time);
    setIsValid(true);

    // Call onChange immediately to update parent component
    if (onChange) {
      onChange(time);
    }

    // Close dropdown immediately for better UX
    setShowDropdown(false);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Prevent background scroll on mobile when dropdown is open
  useEffect(() => {
    if (isMobile() && showDropdown) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = 'unset';
      };
    }
  }, [showDropdown]);

  const validateTime = (time: string): boolean => {
    // Strict validation for 24-hour format (00:00 to 23:59)
    const timeRegex = /^([0-1]\d|2[0-3]):([0-5]\d)$/;
    return timeRegex.test(time);
  };

  // Convert 12-hour format to 24-hour format
  const convert12To24 = (time12h: string): string => {
    // Check if the input is already in 24-hour format
    if (validateTime(time12h)) {
      return time12h;
    }

    // Try to parse 12-hour format with space (e.g., "2:30 PM")
    let match = time12h.match(/^(\d{1,2}):(\d{2})\s*(AM|PM)$/i);
    if (match) {
      const [hours, minutes, period] = match;
      let hour = parseInt(hours, 10);

      // Convert to 24-hour format
      if (period.toUpperCase() === 'PM' && hour < 12) {
        hour += 12;
      } else if (period.toUpperCase() === 'AM' && hour === 12) {
        hour = 0;
      }

      // Format the result
      const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
      return `${formattedHour}:${minutes}`;
    }

    // Try to parse 12-hour format without space (e.g., "2:30PM")
    match = time12h.match(/^(\d{1,2}):(\d{2})(AM|PM)$/i);
    if (match) {
      const [hours, minutes, period] = match;
      let hour = parseInt(hours, 10);

      // Convert to 24-hour format
      if (period.toUpperCase() === 'PM' && hour < 12) {
        hour += 12;
      } else if (period.toUpperCase() === 'AM' && hour === 12) {
        hour = 0;
      }

      // Format the result
      const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
      return `${formattedHour}:${minutes}`;
    }

    // Try to parse just hours and minutes (e.g., "14:30")
    match = time12h.match(/^(\d{1,2}):(\d{2})$/);
    if (match) {
      const [hours, minutes] = match;
      const hour = parseInt(hours, 10);

      // Validate hour and minute
      if (hour >= 0 && hour < 24 && parseInt(minutes, 10) >= 0 && parseInt(minutes, 10) < 60) {
        const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
        return `${formattedHour}:${minutes}`;
      }
    }

    // Try to parse just hours (e.g., "14")
    match = time12h.match(/^(\d{1,2})$/);
    if (match) {
      const hour = parseInt(match[1], 10);

      // Validate hour
      if (hour >= 0 && hour < 24) {
        const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
        return `${formattedHour}:00`;
      }
    }

    return "00:00"; // Invalid format
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Restrict input to valid time patterns only
    // Allow partial inputs during typing (like "1:", "1:3", etc.)
    const timeInputRegex = /^$|^([0-9]{1,2})(:[0-9]{0,2})?\s*(AM|PM|am|pm)?$/;

    if (timeInputRegex.test(newValue)) {
      // Allow any input during typing, but mark as invalid if it doesn't match a valid format
      setInputValue(newValue);

      // Try to convert from 12-hour to 24-hour format
      const time24h = convert12To24(newValue);

      // Check if the input is valid
      if (validateTime(time24h)) {
        setSelectedTime(time24h);
        setIsValid(true);
        if (onChange) {
          onChange(time24h);
        }
      } else {
        setIsValid(false);
      }
    }
  };

  const handleInputBlur = (e: React.FocusEvent) => {
    // Don't close dropdown if focus is moving to a dropdown item
    const relatedTarget = e.relatedTarget as Element;
    if (relatedTarget && relatedTarget.closest('.time-dropdown-item')) {
      return;
    }

    // When the input loses focus, always format the value properly
    // Try to convert the current input value
    const time24h = convert12To24(inputValue);

    if (validateTime(time24h)) {
      // If conversion succeeded, use the converted value
      setSelectedTime(time24h);
      setInputValue(time24h);
      setIsValid(true);
      if (onChange) {
        onChange(time24h);
      }
    } else {
      // If conversion failed, reset to the last valid value
      setInputValue(selectedTime);
      setIsValid(true);
      // Ensure the onChange is called with the valid value
      if (onChange) {
        onChange(selectedTime);
      }
    }

    // Hide dropdown after a short delay to allow for click events
    setTimeout(() => {
      setShowDropdown(false);
    }, 100);
  };

  const handleInputFocus = () => {
    // Prevent keyboard on mobile devices
    if (isMobile()) {
      inputRef.current?.blur();
    }
    setShowDropdown(true);
  };

  const handleInputClick = () => {
    // On mobile, prevent focus and just show dropdown
    if (isMobile()) {
      inputRef.current?.blur();
    }
    setShowDropdown(!showDropdown);
  };

  // Format time to 24-hour format for display (ensure proper formatting)
  const formatTimeFor24Hour = (time: string): string => {
    if (!time) return "";

    // Ensure the time is in HH:MM format
    const [hour, minute] = time.split(":").map(Number);
    const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
    const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
    return `${formattedHour}:${formattedMinute}`;
  };

  return (
    <>
      {/* Mobile overlay to prevent background interaction */}
      {isMobile() && showDropdown && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-[9998]"
          onClick={() => setShowDropdown(false)}
        />
      )}

      <div className={`relative ${className}`} ref={dropdownRef}>
      <div className="flex items-center justify-between">
        {isStartTime ? (
          // For start time, allow input
          <>
            <div className="relative w-full">
              <input
                ref={inputRef}
                type="text"
                value={isValid && inputValue ? formatTimeFor24Hour(inputValue) : (inputValue || "")}
                onChange={handleInputChange}
                onFocus={handleInputFocus}
                onClick={handleInputClick}
                onBlur={handleInputBlur}
                placeholder="00:00"
                readOnly={isMobile()} // Make readonly on mobile to prevent keyboard
                className={`w-full text-sm py-3 px-2.5 border rounded-lg outline-none focus:border-blue-600 ${
                  isValid
                    ? (!inputValue || inputValue === "00:00")
                      ? "text-gray-400 border-gray-300"
                      : "text-gray-900 border-[#D9D9D9]"
                    : "border-red-500 text-red-500"
                } ${isMobile() ? 'cursor-pointer' : ''}`}
                style={{ minHeight: '42px' }}
              />
              <button
                type="button"
                onClick={() => setShowDropdown(!showDropdown)}
                className="absolute right-2 top-1/2 transform -translate-y-1/2"
              >
                <Clock size={20} className="text-primary" />
              </button>
            </div>
          </>
        ) : (
          // For end time, display as disabled and calculated automatically
          <div className="relative w-full">
            <input
              ref={inputRef}
              type="text"
              value={isValid && inputValue ? formatTimeFor24Hour(inputValue) : (inputValue || "")}
              disabled={true}
              placeholder="00:00"
              className={`w-full text-sm py-3 px-2.5 border rounded-lg outline-none bg-gray-100 cursor-not-allowed ${
                isValid
                  ? (!inputValue || inputValue === "00:00")
                    ? "text-gray-400 border-gray-300"
                    : "text-gray-700 border-[#D9D9D9]"
                  : "border-red-500 text-red-500"
              }`}
              style={{ minHeight: '42px' }}
            />
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <Clock size={20} className="text-gray-400" />
            </div>
          </div>
        )}
      </div>

      {/* Show dropdown for both start and end time - using simple relative positioning */}
      <div
        className={`absolute mt-1 w-full max-h-48 overflow-y-auto z-10 bg-white shadow-[0px_4px_12px_0px_#2C58BB1A] rounded-lg transition-all duration-200 ${
          showDropdown
            ? "visible opacity-100 translate-y-0"
            : "invisible opacity-0 -translate-y-5"
        }`}
        style={{
          // Prevent background scroll on mobile when dropdown is open
          ...(isMobile() && showDropdown && {
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '90%',
            maxWidth: '300px',
            maxHeight: '60vh',
            zIndex: 9999,
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'
          })
        }}
      >
        {times.length > 0 ? (
          times.map((time: string) => (
            <div
              key={time}
              onClick={() => handleSelectTime(time)}
              className={`px-4 py-2 cursor-pointer hover:bg-gray-100/20 transition duration-200 text-sm ${
                selectedTime === time
                  ? "bg-blue-600/10 text-blue-600 font-medium"
                  : "text-gray-900"
              }`}
              style={{
                // Ensure proper touch targets on mobile
                ...(isMobile() && {
                  minHeight: '44px',
                  display: 'flex',
                  alignItems: 'center'
                })
              }}
            >
              {formatTimeFor24Hour(time)}
            </div>
          ))
        ) : (
          <div className="px-4 py-2 text-sm text-gray-500">No time options available</div>
        )}
      </div>
    </div>
    </>
  );
};

export default WorkingHoursTimePicker;
