// components/RouteProgressBar.js
"use client"; // Make this a client component for Next.js App Router

import { usePathname } from "next/navigation";
import NProgress from 'nprogress';
// import "nprogress/nprogress.css"; // Import the CSS for nprogress
import { useEffect, useState } from "react";
NProgress.configure({ showSpinner: false }); // Disable the loading spinner

const RouteProgressBar = () => {
    const pathname = usePathname();

    const [mounted, setMounted] = useState(false);
  
    const [visible, setVisible] = useState(false);
  
    useEffect(() => {
      setMounted(true);
    }, []);
  
    useEffect(() => {
      if (!visible) {
        NProgress.start();
        setVisible(true);
      }
  
      if (visible) {
        NProgress.done();
        setVisible(false);
      }
  
      if (!visible && mounted) {
        setVisible(false);
        NProgress.done();
      }
  
      return () => {
        NProgress.done();
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pathname, mounted]);
  
    if (!mounted) {
      return null;
    }

  return null; // This component doesn’t render any visible output
};

export default RouteProgressBar;
