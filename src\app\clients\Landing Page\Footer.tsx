import Image from "next/image";
import Link from "next/link";
import { FC } from "react";
import Logo from "../../../../public/assets/images/client/client-logo-white.svg";
import { FaInstagram, FaLinkedinIn, FaYoutube } from "react-icons/fa";
import { FaMapMarkerAlt } from "react-icons/fa";


const Footer: FC = () => {
  return (
    <>
      {/* Main Footer */}
      <footer
        className="py-16 px-4 text-white bg-[#1B1133]"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Mobile Layout */}
          <div className="block lg:hidden space-y-8">
            {/* Logo */}
            <div className="flex justify-center sm:justify-start">
              <Image
                src={Logo}
                alt="Thought Pudding Logo"
                width={160}
                height={64}
                className="w-auto h-auto max-w-[180px]"
              />
            </div>

            {/* Navigation Sections - Stacked on small mobile, side-by-side on larger mobile */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-8 sm:gap-12">
              {/* About Us */}
              <div className="text-center sm:text-left">
                <h3 className="font-bold text-lg mb-4">About Us</h3>
                <ul className="space-y-3">
                  <li>
                    <Link href="/clients#why-different" className="text-sm hover:text-gray-300 transition-colors">Learn About Therapy</Link>
                  </li>
                  <li>
                    <Link href="/clients#about" className="text-sm hover:text-gray-300 transition-colors">Find Therapist</Link>
                  </li>
                </ul>
              </div>
              
              {/* Legals */}
              <div className="text-center sm:text-left">
                <h3 className="font-bold text-lg mb-4">Legals</h3>
                <ul className="space-y-3">
                  <li>
                    <Link href="/clients/privacy-policy" className="text-sm hover:text-gray-300 transition-colors">
                      Privacy Policy
                    </Link>
                  </li>
                  <li>
                    <Link href="/clients/terms-of-use" className="text-sm hover:text-gray-300 transition-colors">
                      Terms & Conditions
                    </Link>
                  </li>
                  <li>
                    <Link href="/clients/cookies-policy" className="text-sm hover:text-gray-300 transition-colors">Cookies Policy</Link>
                  </li>
                  <li>
                    <Link href="#" className="text-sm hover:text-gray-300 transition-colors">Disclaimer</Link>
                  </li>
                </ul>
              </div>
            </div>

            {/* Social Links */}
            <div className="text-center sm:text-left">
              <h3 className="font-bold text-lg mb-4">Follow Us</h3>
              <div className="flex justify-center sm:justify-start gap-6">
                <Link
                  href="https://www.instagram.com/thoughtpuddingfortherapists/"
                  target="_blank"
                  className="hover:text-gray-300 transition-colors p-2"
                  aria-label="Follow us on Instagram"
                >
                  <FaInstagram size={24} />
                </Link>
                <Link
                  href="https://www.linkedin.com/company/thought-pudding-for-therapists"
                  target="_blank"
                  className="hover:text-gray-300 transition-colors p-2"
                  aria-label="Follow us on LinkedIn"
                >
                  <FaLinkedinIn size={24} />
                </Link>
                <Link 
                  href="#" 
                  target="_blank" 
                  className="hover:text-gray-300 transition-colors p-2"
                  aria-label="Follow us on YouTube"
                >
                  <FaYoutube size={24} />
                </Link>
              </div>
            </div>

            {/* Address Section */}
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <FaMapMarkerAlt className="text-white flex-shrink-0 text-lg mt-1" />
                <div className="text-left space-y-1">
                  <p className="text-sm leading-relaxed">402, 4th floor, Sorrento Building, High Street, Hiranandani Gardens, Powai, Mumbai 400076</p>
                </div>
              </div>
            </div>

            {/* Copyright */}
            <div className="pt-6 border-t border-gray-600 text-center">
              <p className="text-xs uppercase tracking-wide text-gray-400">
                &copy; 2025 Thought Pudding. All Rights Reserved
              </p>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden lg:flex justify-center">
            <div className="flex gap-56 items-start max-w-6xl w-full">
              {/* Left Side - Logo Section */}
              <div className="flex flex-col justify-between min-h-[250px]">
                <div>
                  <Image
                    src={Logo}
                    alt="Thought Pudding Logo"
                    width={250}
                    height={95}
                    className="w-auto h-auto"
                  />
                  <p className="text-xs uppercase tracking-wide font-medium mt-2">
                    &copy; 2025 Thought Pudding. All Rights Reserved
                  </p>
                </div>
              </div>

              {/* Right Side - All Content Sections */}
              <div className="space-y-8">
                {/* Top Row - About Us, Legals, Follow Us */}
                <div className="flex gap-24">
                  {/* About Us */}
                  <div className="min-w-[170px]">
                    <h3 className="font-bold text-xl mb-5">
                      About Us
                    </h3>
                    <ul className="space-y-3">
                      <li>
                        <Link href="/clients#why-different" className="text-base hover:text-gray-300 font-medium">Learn About Therapy</Link>
                      </li>
                      <li>
                        <Link href="/clients#about" className="text-base hover:text-gray-300 font-medium">Find Therapist</Link>
                      </li>
                    </ul>
                  </div>

                  {/* Legals */}
                  <div className="min-w-[160px]">
                    <h3 className="font-bold text-xl mb-5">Legals</h3>
                    <ul className="space-y-3">
                      <li>
                        <Link href="/clients/privacy-policy" className="text-base hover:text-gray-300 font-medium">
                          Privacy Policy
                        </Link>
                      </li>
                      <li>
                        <Link href="/clients/terms-of-use" className="text-base hover:text-gray-300 font-medium">
                          Terms & Conditions
                        </Link>
                      </li>
                      <li>
                        <Link href="/clients/cookies-policy" className="text-base hover:text-gray-300 font-medium">Cookies Policy</Link>
                      </li>
                      <li>
                        <Link href="#" className="text-base hover:text-gray-300 font-medium">Disclaimer</Link>
                      </li>
                    </ul>
                  </div>

                  {/* Social Links */}
                  <div className="min-w-[110px]">
                    <h3 className="font-bold text-xl mb-5">
                      Follow Us
                    </h3>
                    <div className="flex gap-3">
                      <Link
                        href="https://www.instagram.com/thoughtpuddingfortherapists/"
                        target="_blank"
                        className="hover:text-gray-300"
                      >
                        <FaInstagram size={26} />
                      </Link>
                      <Link
                        href="https://www.linkedin.com/company/thought-pudding-for-therapists"
                        target="_blank"
                        className="hover:text-gray-300"
                      >
                        <FaLinkedinIn size={26} />
                      </Link>
                      <Link href="#" className="hover:text-gray-300">
                        <FaYoutube size={26} />
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Bottom Row - Address Section */}
                <div className="pt-6">
                  <div className="flex items-start gap-5">
                    <FaMapMarkerAlt className="text-white flex-shrink-0 text-lg mt-1" />
                    <div className="text-left space-y-1">
                      <p className="text-base leading-relaxed">402, 4th floor, Sorrento Building, High Street, Hiranandani Gardens, Powai, Mumbai 400076</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Disclaimer Section */}
      <section className="bg-white py-8 px-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-black text-sm sm:text-base italic leading-relaxed">
            <p className="mb-4">
              <strong>Disclaimer:</strong> Kindly note we are not a platform for emergency services or suicide prevention in mental health. For emergencies please find help by clicking <Link href="https://icallhelpline.org/" target="_blank" className="text-blue-600 underline hover:text-blue-800 transition-colors">here</Link>.
            </p>
            <p className="mb-4">
              We do not own, operate or control the helpline numbers listed in the directory above.
              The helpline numbers are listed for referral purposes only, and Thought Pudding does not make any recommendations or guarantees regarding the quality of response and medical advice you might receive from any of the helplines.
            </p>
            <p>
              Thought Pudding does not endorse these helplines and makes no representations, warranties or guarantees as to, and assumes no responsibility for, the services provided by these entities.
              Thought Pudding disclaims all liability for damages of any kind arising out of calls made to these helpline numbers.
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default Footer; 