import SplashScreen from "@/components/common/SplashScreen";
import { useAuth } from "@/context/AuthContext";
import { usePathname, useRouter } from "next/navigation";
import React, { Suspense, useEffect, useState } from "react";
import Header from "./Header";
import SettingSidebar from "./SettingSidebar";
import CommonModal from "@/components/dashboard/CommonModal";
import { ValidSubscription } from "../../../public/assets/Svgs";
import Link from "next/link";
import { useGetValidSubscription } from "@/services/dashboard.service";

const SettingLayout: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, setIsAuthenticated, loading } = useAuth() || {
    isAuthenticated: false,
    loading: true, // Add loading as a default state if not provided
  };

  const [validUser, setValidUser] = useState(false);
  const { validSubscriptionData } = useGetValidSubscription();

  useEffect(() => {
    if (!loading && !isAuthenticated && pathname !== "/") {
      router.replace("/");
    }
    if (
      validSubscriptionData?.valid === false &&
      pathname !== "/setting/subscription"
    ) {
      setValidUser(true);
      router.replace("/dashboard");
    }
  }, [isAuthenticated, router, pathname, loading]);
  return (
    <div className="px-6 pt-3 pb-8 bg-[#F5F5F7] h-screen ">
      <div className="dashboard_container h-full">
        {/* header */}
        <Header setIsAuthenticated={setIsAuthenticated} />

        {/* sidebar and content */}
        <div className="mt-5 sm:flex bg-white sm:h-[calc(100%_-_120px)] rounded-xl overflow-hidden">
          {/* sidebar  */}
          <div className="min-w-[255px] sm:h-full">
            <SettingSidebar />
          </div>
          {/* content */}

          <div className="flex-1 p-5 overflow-y-auto">
            <Suspense fallback={<SplashScreen />}>{children}</Suspense>
          </div>
        </div>
      </div>
      {validSubscriptionData?.valid === false && (
        <CommonModal
          title=""
          isClose={validUser}
          remove
          setIsClose={setValidUser}
        >
          <div className="text-center flex flex-col items-center">
            <ValidSubscription className={`w-[90px] h-[90px]`} />
            <h2 className="text-2xl text-primary font-semibold pt-4">
              Subscription Expired
            </h2>
            <p className="text-base text-primary/70 pt-2">
              Uh-oh! Your subscription has ended. Please renew your subscription{" "}
              <Link
                href={`/setting/subscription`}
                className="underline text-yellow-600 font-medium"
              >
                here.
              </Link>
            </p>
          </div>
        </CommonModal>
      )}
    </div>
  );
};

export default SettingLayout;
