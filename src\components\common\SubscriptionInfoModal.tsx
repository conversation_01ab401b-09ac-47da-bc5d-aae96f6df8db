import { AuthService } from "@/services/auth.service";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import Modal from "./Modal";
import Input from "@/components/common/Input";
import Button from "@/components/common/Button";
import { useFetchTherapistData } from "@/context/TherapistContext";

interface Address {
  streetAddress: string;
  pincode: string;
  district: string;
  state: string;
}

interface TherapistData {
  _id: string;
  name: string;
  email: string;
  panCard?: string;
  gstNumber?: string;
  address?: Address;
}

interface Props {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  onSubmitted: () => void;
}


const SubscriptionInfoModal: React.FC<Props> = ({
  visible,
  onClose,
  onSuccess,
  onSubmitted,
}) => {
  const [form, setForm] = useState({
    street: "",
    pincode: "",
    district: "",
    state: "",
    pan: "",
    hasGst: "no",
    gstNumber: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const { therapistData } = (useFetchTherapistData() || {
    therapistData: null,
  }) as {
    therapistData: TherapistData | null;
  };

  useEffect(() => {
    if (therapistData) {
      setForm({
        street: therapistData.address?.streetAddress || "",
        pincode: therapistData.address?.pincode || "",
        district: therapistData.address?.district || "",
        state: therapistData.address?.state || "",
        pan: therapistData.panCard || "",
        gstNumber: therapistData.gstNumber || "",
        hasGst: therapistData.gstNumber ? "yes" : "no",
      });
    }
  }, [therapistData]);

  const validate = () => {
    const newErrors: Record<string, string> = {};
    if (!/^\d{6}$/.test(form.pincode))
      newErrors.pincode = "Pincode must be 6 digits";
    if (!form.district) newErrors.district = "District is required";
    if (!form.state) newErrors.state = "State is required";
    if (!/^[A-Z]{5}[0-9]{4}[A-Z]$/.test(form.pan))
      newErrors.pan = "PAN must be 10 characters (e.g., **********)";
    if (form.hasGst === "yes" && !form.gstNumber)
      newErrors.gstNumber = "GST number is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const fetchLocationFromPincode = async (pincode: string) => {
    if (/^\d{6}$/.test(pincode)) {
      try {
        const response = await fetch(
          `https://api.postalpincode.in/pincode/${pincode}`
        );
        const data = await response.json();
        const location = data[0]?.PostOffice?.[0];
        if (location) {
          setForm((prev) => ({
            ...prev,
            district: location.District,
            state: location.State,
          }));
          setErrors((prev) => ({ ...prev, district: "", state: "" }));
        }
      } catch (error) {
        console.error("Pincode API error:", error);
      }
    }
  };

  const handlePincodeChange = (value: string) => {
    const numericValue = value.replace(/\D/g, "").slice(0, 6);
    setForm((prev) => ({ ...prev, pincode: numericValue }));
    if (numericValue.length === 6) fetchLocationFromPincode(numericValue);
  };

  const handlePanChange = (value: string) => {
    setForm((prev) => ({ ...prev, pan: value.toUpperCase() }));
  };

  const handleSubmit = async () => {
    if (!validate()) return;
  
    if (!form.street || !form.pincode || !form.district || !form.state) {
      toast.error("All address fields must be filled out");
      return;
    }
  
    try {
      if (!therapistData?._id) throw new Error("Therapist ID is missing");
  
      const payload = {
        address: {
          streetAddress: form.street,
          pincode: form.pincode,
          district: form.district,
          state: form.state,
        },
        panCard: form.pan,
        ...(form.hasGst === "yes" && { gstNumber: form.gstNumber }),
      };
  
      await AuthService.updateTherapistProfile(therapistData._id, payload);
  
      toast.success("Details updated successfully");
      onSuccess();
      onSubmitted();
    } catch (err) {
      console.error("Update failed:", err);
      toast.error("Failed to update details");
    }
  };
  

  return (
    <Modal open={visible} handler={onClose}>
      <div className="space-y-4">
        <p>
          <strong>Name:</strong> {therapistData?.name || "N/A"}
        </p>
        <p>
          <strong>Email:</strong> {therapistData?.email || "N/A"}
        </p>

        <div>
          <label className="block mb-1 text-sm font-medium">
            Street Address
          </label>
          <Input
            name="street"
            value={form.street}
            onChange={(e) => setForm({ ...form, street: e.target.value })}
          />
        </div>

        <div>
          <label className="block mb-1 text-sm font-medium">Pincode *</label>
          <Input
            name="pincode"
            type="text"
            value={form.pincode}
            onChange={(e) => handlePincodeChange(e.target.value)}
          />
          {errors.pincode && (
            <p className="text-red-500 text-sm">{errors.pincode}</p>
          )}
        </div>

        <div>
          <label className="block mb-1 text-sm font-medium">District *</label>
          <Input
            name="district"
            value={form.district}
            onChange={(e) => setForm({ ...form, district: e.target.value })}
          />
          {errors.district && (
            <p className="text-red-500 text-sm">{errors.district}</p>
          )}
        </div>

        <div>
          <label className="block mb-1 text-sm font-medium">State *</label>
          <Input
            name="state"
            value={form.state}
            onChange={(e) => setForm({ ...form, state: e.target.value })}
          />
          {errors.state && (
            <p className="text-red-500 text-sm">{errors.state}</p>
          )}
        </div>

        <div>
          <label className="block mb-1 text-sm font-medium">PAN Card *</label>
          <Input
            name="pan"
            value={form.pan}
            onChange={(e) => handlePanChange(e.target.value)}
          />
          {errors.pan && <p className="text-red-500 text-sm">{errors.pan}</p>}
        </div>

        <div>
          <label className="block mb-1 text-sm font-medium">
            Do you have a GST number?
          </label>
          <div className="flex items-center gap-4 mt-1">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                value="yes"
                checked={form.hasGst === "yes"}
                onChange={() => setForm({ ...form, hasGst: "yes" })}
              />
              Yes
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                value="no"
                checked={form.hasGst === "no"}
                onChange={() =>
                  setForm({ ...form, hasGst: "no", gstNumber: "" })
                }
              />
              No
            </label>
          </div>
        </div>

        {form.hasGst === "yes" && (
          <div>
            <label className="block mb-1 text-sm font-medium">
              GST Number *
            </label>
            <Input
              name="gstNumber"
              value={form.gstNumber}
              onChange={(e) => setForm({ ...form, gstNumber: e.target.value })}
            />
            {errors.gstNumber && (
              <p className="text-red-500 text-sm">{errors.gstNumber}</p>
            )}
          </div>
        )}

        <div className="flex justify-end gap-3 pt-2">
          <Button variant="ghost" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>Continue to Payment</Button>
        </div>
      </div>
    </Modal>
  );
};

export default SubscriptionInfoModal;
