"use client";

import <PERSON>rip<PERSON> from "next/script";
import { useEffect } from "react";

const GA_TRACKING_ID = "G-28XZ1XLEJK";

declare global {
  interface PageviewEvent {
    event: "pageview";
    pagePath: string;
  }

  interface Window {
    dataLayer: Array<PageviewEvent>;
    dataLayerPush: (event: PageviewEvent) => void;
  }
}

export default function GoogleAnalytics() {
  useEffect(() => {
    if (typeof window !== "undefined" && GA_TRACKING_ID) {
      // Ensure dataLayer exists
      if (!window.dataLayer) {
        window.dataLayer = [];
      }

      // Push the initial GTM config if dataLayer is empty
      if (!window.dataLayerPush) {
        window.dataLayerPush = function (...args: PageviewEvent[]) {
          window.dataLayer.push(...args);
        };
      }

      // Initial page view event
      window.dataLayerPush({
        event: "pageview",
        pagePath: window.location.pathname,
      });
    }
  }, []);

  return (
    <>
      {GA_TRACKING_ID && (
        <>
          <Script
            strategy="afterInteractive"
            src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
          />
          <Script
            id="google-analytics"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${GA_TRACKING_ID}', {
                  page_path: window.location.pathname,
                });
              `,
            }}
          />
        </>
      )}
    </>
  );
}
