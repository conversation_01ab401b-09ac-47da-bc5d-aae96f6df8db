"use client";

interface SessionFeesSelectorProps {
  minFee: number;
  maxFee: number;
  selectedSessionMode?: string;
  isMobile?: boolean;
}

export default function SessionFeesSelector({
  minFee,
  maxFee,
  selectedSessionMode,
  isMobile = false,
}: SessionFeesSelectorProps) {
  // Check if the selected session mode is introductory
  const isIntroductorySession = selectedSessionMode?.toLowerCase().includes("introductory") || false;
  return (
    <div className={isMobile ? "mb-6" : "mb-6 mx-4"}>
      {!isMobile ? (
        <div className="flex gap-8 items-center">
          <div className="flex flex-col">
            <h2 className="text-gray-500 text-xs mb-1">STEP 02</h2>
            <h3 className="text-[#251D5C] font-semibold gilmer-bold">
              Select Session Fees
            </h3>
          </div>
          <div className="flex-1">
            <div className="border border-[#718FFF] bg-[#718FFF]/10 rounded-xl p-3 flex justify-between items-center cursor-pointer transition-colors">
              <div className="flex items-center">
                <div className="w-4 h-4 rounded-full border border-[#718FFF] flex items-center justify-center mr-3">
                  <div className="w-2 h-2 rounded-full bg-[#718FFF]"></div>
                </div>
                <div>
                  <p className="font-medium text-[#251D5C] text-sm">
                    1 session
                  </p>
                  <p className="text-xs text-gray-500">
                    Get a single session
                  </p>
                </div>
              </div>
              <div className="font-semibold text-[#251D5C] text-sm">
                {isIntroductorySession ? "Free" : `₹${minFee} - ₹${maxFee} / session`}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <>
          <h3 className="text-[#251D5C] font-semibold mb-3 gilmer-bold">
            Select Session Fees
          </h3>
          <div className="border border-[#718FFF] bg-[#718FFF]/10 rounded-xl p-3 flex justify-between items-center cursor-pointer transition-colors">
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full border border-[#718FFF] flex items-center justify-center mr-3">
                <div className="w-2 h-2 rounded-full bg-[#718FFF]"></div>
              </div>
              <div>
                <p className="font-medium text-[#251D5C] text-sm">
                  1 session
                </p>
                <p className="text-xs text-gray-500">
                  Get a single session
                </p>
              </div>
            </div>
            <div className="font-semibold text-[#251D5C] text-sm">
              {isIntroductorySession ? "Free" : `₹${minFee} - ₹${maxFee} / session`}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
