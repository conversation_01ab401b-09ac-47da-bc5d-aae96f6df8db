"use client";

import React from "react";
import Image from "next/image";
import { <PERSON>o } from 'next/font/google';

const roboto = Roboto({
  weight: ['400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
});

export default function TherapistProfileLoading() {
  return (
    <div
      className={`bg-[#2A1B6D] min-h-screen text-white p-4 md:p-6 ${roboto.className}`}
      style={{
        backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundBlendMode: 'overlay'
      }}
    >
      <div className="container mx-auto">
        <div className="flex justify-center mb-6">
          <Image
            src="/assets/images/newHome/therapist-profile-logo.png"
            alt="Thought Pudding Logo"
            width={200}
            height={50}
            className="h-auto"
          />
        </div>

        <div className="flex justify-center items-center h-[70vh]">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-white"></div>
            <p className="text-xl font-medium mt-4">Loading therapist profile...</p>
          </div>
        </div>
      </div>
    </div>
  );
}
