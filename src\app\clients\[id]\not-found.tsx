"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Roboto } from 'next/font/google';

const roboto = Roboto({
  weight: ['400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
});

export default function TherapistNotFound() {
  return (
    <div
      className={`bg-[#2A1B6D] min-h-screen text-white flex flex-col items-center justify-center p-4 md:p-6 ${roboto.className}`}
      style={{
        backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundBlendMode: 'overlay'
      }}
    >
      <div className="container mx-auto max-w-2xl">
        <div className="flex justify-center mb-8">
          <Image
            src="/assets/images/newHome/therapist-profile-logo.png"
            alt="Thought Pudding Logo"
            width={200}
            height={50}
            className="h-auto"
          />
        </div>

        <div className="bg-white text-black rounded-xl p-8 text-center">
          <h1 className="text-3xl font-bold text-[#6D84FF] mb-4">Therapist Not Found</h1>
          <p className="text-lg mb-6">
            We couldn&apos;t find the therapist profile you&apos;re looking for. The therapist may not exist or the URL might be incorrect.
          </p>
          <Link href="/" className="bg-[#1A1B4B] text-white px-6 py-3 rounded-md hover:opacity-90 font-medium inline-block">
            Return to Home
          </Link>
        </div>
      </div>
    </div>
  );
}
