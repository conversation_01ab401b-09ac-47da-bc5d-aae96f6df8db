"use client";
import React, { Fragment, Suspense, useEffect, useState } from "react";
import { useAuth } from "@/context/AuthContext";
import { usePathname, useRouter } from "next/navigation";
import SplashScreen from "@/components/common/SplashScreen";
import Header from "./Header";
import CommonModal from "@/components/dashboard/CommonModal";
import { ValidSubscription } from "../../../public/assets/Svgs";
import Link from "next/link";
import { useGetValidSubscription } from "@/services/dashboard.service";
import { setUsetifulTags } from "usetiful-sdk";
import { useGetSettingData } from "@/services/setting.service";

const DashboardLayout: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, setIsAuthenticated, loading } = useAuth() || {
    isAuthenticated: false,
    loading: true,
  };
  const [validUser, setValidUser] = useState(true);
  const { validSubscriptionData } = useGetValidSubscription();
  const { therapistData } = useGetSettingData();
  const [usetifulInitialized, setUsetifulInitialized] = useState(false);

  // Redirect to therapist page if not authenticated
  useEffect(() => {
    if (!loading && !isAuthenticated && pathname !== "/therapist") {
      router.replace("/therapist");
    }
  }, [isAuthenticated, router, loading, pathname]);

  // Enforce dashboard route if subscription is invalid
  useEffect(() => {
    if (validSubscriptionData?.valid === false) {
      router.replace("/dashboard");
    }
  }, [pathname, validSubscriptionData?.valid, router]);

  useEffect(() => {
    if (validSubscriptionData?.valid === false) {
      document.body.style.overflow = "hidden";
    }
  }, [validSubscriptionData?.valid]);

  useEffect(() => {
    if (therapistData && !usetifulInitialized) {
      setUsetifulTags({
        userId: therapistData._id,
        firstName: therapistData.name,
        email: therapistData.email,
      });
      setUsetifulInitialized(true);
    }
  }, [therapistData, usetifulInitialized]);
  return (
    <Fragment>
      <div className="md:px-6 px-4 pt-3 pb-8 bg-[#F5F5F7] max-h-full">
        <div className="dashboard_container">
          <Header setIsAuthenticated={setIsAuthenticated || (() => {})} />
          <Suspense fallback={<SplashScreen />}>
            {/* Only render children if subscription is valid */}
            {children}
          </Suspense>
        </div>
        {/* Show modal if subscription is invalid */}
        {validSubscriptionData?.valid === false && (
          <CommonModal
            title=""
            isClose={validUser}
            remove
            setIsClose={setValidUser}
          >
            <div className="text-center flex flex-col items-center">
              <ValidSubscription className="w-[90px] h-[90px]" />
              <h2 className="text-2xl text-primary font-semibold pt-4">
                {therapistData?.hasUsedTrial
                  ? "Subscription Expired"
                  : "Team Freud or Team Beck?"}
              </h2>
              <p className="text-base text-primary/70 pt-2">
                {therapistData?.hasUsedTrial ? (
                  <>
                    Uh-oh! Your subscription has ended. Please renew your
                    subscription{" "}
                    <Link
                      href={`/setting/subscription`}
                      className="underline text-yellow-600 font-medium"
                    >
                      here.
                    </Link>
                  </>
                ) : (
                  <>
                    Okay, not literally— all we mean is choose the subscription plan that suits you{" "}
                    <Link
                      href={`/setting/subscription`}
                      className="underline text-yellow-600 font-medium"
                    >
                      here.
                    </Link>
                  </>
                )}
              </p>
            </div>
          </CommonModal>
        )}
      </div>
    </Fragment>
  );
};

export default DashboardLayout;
