import axios from "axios";
import endpoints from "@/utils/endpoints";

// Define API response interface
export interface ApiResponse<T = unknown> {
  status: string;
  message: string;
  data: T;
  responseCode: number;
  errors?: Array<{ code: string; message: string }>;
}

// Define booking data interface
export interface BookingData {
  therapistId: string;
  therapistIdentifier: string;
  sessionMode: string;
  slotType?: string;
  date: string;
  startTime: string;
  endTime: string;
  duration: number;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  clientGender?: string;
  clientAge?: number;
}

// Define OTP verification interface
export interface OtpVerificationData {
  email: string;
  otp: string;
}

// Define client check interface
export interface ClientCheckData {
  clientEmail: string;
  therapistId: string;
}

// Define client check response interface
export interface ClientCheckResponse {
  clientExists: boolean;
}

/**
 * Send OTP to the provided email address
 * @param email Email address to send OTP to
 * @returns API response
 */
export const sendOtp = async (email: string): Promise<ApiResponse<unknown>> => {

  try {
    // Create a direct axios instance for public endpoints
    const baseApiUrl = process.env.NEXT_PUBLIC_API_URL;

    const directAxios = axios.create({
      baseURL: baseApiUrl,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Make the API call to send OTP with the exact payload structure
    const payload = { email };
    const response = await directAxios.put(endpoints.otp.send, payload);
    return response.data;
  } catch (error) {
    console.error("Error sending OTP:", error);
    throw error;
  }
};

/**
 * Verify OTP
 * @param data OTP verification data
 * @returns API response
 */
export const verifyOtp = async (data: OtpVerificationData): Promise<ApiResponse<unknown>> => {

  try {
    // Create a direct axios instance for public endpoints
    const baseApiUrl = process.env.NEXT_PUBLIC_API_URL ;

    const directAxios = axios.create({
      baseURL: baseApiUrl,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Make the API call to verify OTP with the exact payload structure
    const payload = {
      email: data.email,
      otp: data.otp
    };

    const response = await directAxios.put(endpoints.otp.verify, payload);

    return response.data;
  } catch (error) {
    console.error("Error verifying OTP:", error);
    throw error;
  }
};

/**
 * Check if client exists for a therapist
 * @param data Client check data
 * @returns API response with client existence status
 */
export const checkClient = async (data: ClientCheckData): Promise<ApiResponse<ClientCheckResponse>> => {
  try {
    const baseApiUrl = process.env.NEXT_PUBLIC_API_URL;

    const directAxios = axios.create({
      baseURL: baseApiUrl,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const payload = {
      clientEmail: data.clientEmail,
      therapistId: data.therapistId
    };

    const response = await directAxios.post<ApiResponse<ClientCheckResponse>>(
      endpoints.clients.checkClient,
      payload
    );

    return response.data;
  } catch (error) {
    console.error("Error checking client:", error);
    throw error;
  }
};


/**
 * Schedule a booking
 * @param bookingData Booking data
 * @returns API response
 */
export const scheduleBooking = async (bookingData: BookingData): Promise<ApiResponse<unknown>> => {

  try {
    // Create a direct axios instance for public endpoints
    const baseApiUrl = process.env.NEXT_PUBLIC_API_URL;

    const directAxios = axios.create({
      baseURL: baseApiUrl,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const response = await directAxios.post(endpoints.booking.schedule, bookingData);
    return response.data;
  } catch (error) {
    console.error("Error scheduling booking:", error);
    throw error;
  }
};
