import React from "react";
import Flatpickr from "react-flatpickr";
import "flatpickr/dist/flatpickr.css";
import { CalendarDots } from "@phosphor-icons/react";

interface DatePickerProps {
  className?: string;
  value?: string; // Add value prop
  onChange: (date: string) => void; // Add onChange prop
  placeholder?: string;
  minDateToday?: boolean; // Add a prop to conditionally set minDate
  minDate?: string; // Add this prop
}

const DatePicker: React.FC<DatePickerProps> = ({
  className,
  value,
  onChange,
  placeholder,
  minDateToday = false,
  minDate, // Add this prop
}) => {
  return (
    <div className={`mt-2 relative ${className}`}>
      <Flatpickr
        className="py-3 pl-2.5 pr-[30px] border border-[#D9D9D9] rounded-lg w-full outline-none text-sm_18 text-primary"
        value={value ? new Date(value) : undefined}
        onChange={(dates) => onChange(dates[0]?.toISOString() || "")} // Handle date change
        options={{
          dateFormat: "d/m/Y", // You can adjust the format as needed
          disableMobile: true, // Force Flatpickr UI on mobile
          ...(minDateToday ? { minDate: "today" } : {}), // Disable dates before today
          ...(minDate ? { minDate: minDate } : {}), // Add this line
        }}
        placeholder={placeholder}
      />
      <CalendarDots
        size={20}
        className="absolute top-1/2 right-3 -translate-y-1/2"
      />
    </div>
  );
};

export default DatePicker;
