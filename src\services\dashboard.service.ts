import axiosInstance, { fetcher } from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import { useMemo } from "react";
import toast from "react-hot-toast";
import useSWR from "swr";

const swrOptions = {
  revalidateIfStale: false,
  revalidateOnFocus: false,
  revalidateOnReconnect: false,
};

const swrOptionsSyncEvent = {
  revalidateIfStale: true,
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
};

type ScheduleData = {
  appointments: Array<unknown>;
  appointmentsCount: number;
  stats: unknown;
  clients: Array<unknown>;
};

// get payment listing
export function useGetDashboardStats(startDate: Date, endDate: Date) {
  const query = `startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`;
  const url = `${endpoints.dashboard.getDashboardStats}?${query}`;

  const { data, isLoading, error, isValidating } = useSWR<ScheduleData>(
    url,
    fetcher,
    {
      ...swrOptions,
      revalidateIfStale: true,
    }
  );

  const memoizedValue = useMemo(
    () => ({
      dashboardStatsData: data || [],
      dashboardStatsLoading: isLoading,
      dashboardStatsError: error,
      dashboardStatsValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );

  return memoizedValue;
}

// create schedule
export async function createSchedule(formData: Record<string, unknown>) {
  const toastId = toast.loading(`Scheduling session for ${formData.name}`, {
    duration: Infinity,
  });
  try {
    const url = `${endpoints.dashboard.createSchedule}`;
    const res = await axiosInstance.post(url, formData);
    toast.dismiss(toastId);
    toast.success("Your session has been scheduled successfully!");
    return res?.data;
  } catch (error) {
    toast.dismiss(toastId);
    return error;
  }
}

interface FreeSlot {
  fromDate: string;
  toDate: string;
}

interface FreeSlotsResponse {
  slots: FreeSlot[];
}

interface UseGetFreeSlotsReturn {
  freeSlotData: FreeSlot[];
  freeSlotLoading: boolean;
  freeSlotError: Error | undefined;
  freeSlotValidating: boolean;
}

export function useGetFreeSlots(freeSlot: boolean): UseGetFreeSlotsReturn {
  const url = freeSlot ? `${endpoints.dashboard.getFreeSlot}` : null;

  const { data, error, isLoading, isValidating } = useSWR<FreeSlotsResponse>(
    url,
    fetcher
  );

  const memoizedValue = useMemo(
    () => ({
      freeSlotData: data?.slots || [],
      freeSlotLoading: isLoading,
      freeSlotError: error,
      freeSlotValidating: isValidating,
    }),
    [data?.slots, error, isLoading, isValidating]
  );

  return memoizedValue;
}

// //find me free slot
// export async function findFreeSlot(formData: Record<string, unknown>) {
//   try {
//     const url = `${endpoints.dashboard.findfreeSlot}`;
//     const res = await axiosInstance.post(url, formData);
//     toast.success('Your session has been scheduled successfully!')
//     return res?.data;
//   } catch (error) {
//     const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
//     toast.error(errorMessage);
//     return error;
//   }
// }

// get most recent client request
export function useGetMostRecentClientRequest() {
  const url = `${endpoints.dashboard.getMostRecentClient}`;

  const { data, isLoading, error, isValidating } = useSWR<ScheduleData>(
    url,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      mostRecentClientData: data?.clients || [],
      mostRecentClientLoading: isLoading,
      mostRecentClientError: error,
      mostRecentClientValidating: isValidating,
    }),
    [data?.clients, error, isLoading, isValidating]
  );

  return memoizedValue;
}

export function useGetCancellation({
  page,
  pageSize,
  startDate,
  endDate,
}: {
  page: number;
  pageSize: number;
  startDate: Date;
  endDate: Date;
}) {
  const query = `pageSize=${pageSize}&page=${page}&startDate=${startDate?.toISOString()}&endDate=${endDate?.toISOString()}`;
  const url = `${endpoints.dashboard.getCancellation}?${query}`;

  const { data, isLoading, error, isValidating } = useSWR(
    url,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      cancellationData: data || [],
      cancellationLoading: isLoading,
      cancellationError: error,
      cancellationValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );

  return memoizedValue;
}

// get single client detail
export async function getClientDetails(email: string) {
  // console.log(query , ".query.....")
  // console.log(url , "url")

  // const { data, isLoading, error, isValidating } = useSWR(
  //   url,
  //   fetcher,
  //   swrOptions
  // );

  // console.log(data , "data...............")

  // const memoizedValue = useMemo(
  //   () => ({
  //     clientDetails: data || [],
  //     clientDetailsLoading: isLoading,
  //     clientDetailsError: error,
  //     clientDetailsValidating: isValidating,
  //   }),
  //   [data, error, isLoading, isValidating]
  // );

  try {
    const query = `email=${email}`;
    const url = `${endpoints.dashboard.getClientDetails}?${query}`;

    const res = await axiosInstance.get(url);

    return res?.data;
  } catch (error) {
    return error;
  }

  // return memoizedValue;
}

// export async function getValidSubscription() {
//   try {
//     // const query = `email=${email}`;
//     const url = `${endpoints.setting.subscription.validTherapistSubscription}`;

//     const res = await axiosInstance.get(url);

//     console.log(res, "res............");
//     return res?.data[0];
//   } catch (error) {
//     return error;
//   }

//   // return memoizedValue;
// }

export function useGetValidSubscription() {
  const url = `${endpoints.setting.subscription.validTherapistSubscription}`;

  const { data, mutate } = useSWR(url, fetcher, swrOptions);
  const memoizedValue = useMemo(
    () => ({
      validSubscriptionData: data || [],
      refetchValidSubscription: mutate,
    }),
    [data, mutate]
  );


  return memoizedValue;
}

// sync calendar status check
export function useCheckSyncCalendarStatus() {
  const url = `${endpoints.dashboard.checkSyncStatus}`;

  const { data } = useSWR(url, fetcher, swrOptionsSyncEvent);
  const memoizedValue = useMemo(
    () => ({
      syncEventStatus: data?.data?.syncable || false,
    }),
    [data?.data?.syncable]
  );

  return memoizedValue;
}
