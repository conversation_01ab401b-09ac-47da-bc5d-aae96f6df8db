/* Custom scrollbar styles for analytics accordion - thin and short design */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 2px; /* Make it thinner */
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  margin: 40px 0; /* Increase margin to make track much shorter */
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
  min-height: 20px; /* Smaller minimum thumb height */
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* Hide scrollbar when not needed */
.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Container for scrollable content with custom scrollbar */
.scrollable-container {
  position: relative;
  max-height: 400px; /* Reduced max height */
  overflow-y: auto;
  padding-right: 6px; /* Reduced padding for thinner scrollbar */
}

/* Ensure content is fully visible and scrollable */
.scrollable-content {
  padding-bottom: 32px; /* Increased bottom padding for better visual spacing */
  margin-right: 2px; /* Reduced margin for thinner scrollbar */
}
