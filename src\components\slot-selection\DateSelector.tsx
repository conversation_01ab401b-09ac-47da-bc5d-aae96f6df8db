"use client";

interface DateSelectorProps {
  selectedDate: Date | null;
  currentMonth: Date;
  datesWithSlots: Set<string>;
  onDateSelect: (date: Date) => void;
  onPrevMonth: () => void;
  onNextMonth: () => void;
  formatDateForAPI?: (date: Date) => string; // Make this optional since we're not using it anymore
  isMobile?: boolean;
  onOpenCalendarModal?: () => void;
}

export default function DateSelector({
  selectedDate,
  currentMonth,
  onDateSelect,
  onPrevMonth,
  onNextMonth,
  isMobile = false,
  onOpenCalendarModal,
}: DateSelectorProps) {
  // Calendar helper functions
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  if (isMobile) {
    return (
      <div className="mb-6">
        <h3 className="text-[#251D5C] font-semibold mb-3 gilmer-bold">
          Select The Date
        </h3>
        <div
          className="border border-gray-200 rounded-xl p-3 cursor-pointer"
          onClick={onOpenCalendarModal}
        >
          <div className="w-full text-[#251D5C] text-sm">
            {selectedDate
              ? selectedDate.toLocaleDateString("en-US", {
                  weekday: "short",
                  day: "numeric",
                  month: "short",
                  year: "numeric",
                })
              : "Select a date"}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-[40%] bg-white border border-gray-200 rounded-xl p-5 shadow-sm hover:bg-[#718FFF]/5 transition-colors">
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={onPrevMonth}
          className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100"
        >
          <span className="text-gray-500">&lt;</span>
        </button>
        <h3 className="text-lg font-medium gilmer-medium">
          {currentMonth.toLocaleString("default", {
            month: "long",
            year: "numeric",
          })}
        </h3>
        <button
          onClick={onNextMonth}
          className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100"
        >
          <span className="text-gray-500">&gt;</span>
        </button>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-2">
        {/* Day headers */}
        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
          (day, index) => (
            <div
              key={index}
              className="text-center text-sm font-medium text-gray-500 py-2 gilmer-medium"
            >
              {day}
            </div>
          )
        )}

        {/* Calendar days */}
        {(() => {
          const year = currentMonth.getFullYear();
          const month = currentMonth.getMonth();
          const daysInMonth = getDaysInMonth(year, month);
          const firstDay = getFirstDayOfMonth(year, month);
          const today = new Date();

          // Create an array for all days in the month
          const days = [];

          // Add empty cells for days before the first day of the month
          for (let i = 0; i < firstDay; i++) {
            days.push(
              <div
                key={`empty-${i}`}
                className="h-10 w-10 mx-auto"
              ></div>
            );
          }

          // Add cells for each day of the month
          for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(year, month, day);
            const isToday =
              today.getDate() === day &&
              today.getMonth() === month &&
              today.getFullYear() === year;
            const isSelected =
              selectedDate &&
              selectedDate.getDate() === day &&
              selectedDate.getMonth() === month &&
              selectedDate.getFullYear() === year;
            const isPastDate = date < today && !isToday;

            days.push(
              <div
                key={day}
                className="text-center py-1"
                onClick={() => !isPastDate && onDateSelect(date)}
              >
                <div
                  className={`
                    h-10 w-10 mx-auto flex items-center justify-center text-sm font-medium
                    ${
                      isPastDate
                        ? "text-gray-300 cursor-not-allowed"
                        : isSelected
                        ? "bg-[#2C58BB] text-white rounded-full cursor-pointer"
                        : "text-gray-700 cursor-pointer hover:bg-gray-100"
                    }
                  `}
                >
                  {day}
                </div>
              </div>
            );
          }

          return days;
        })()}
      </div>
    </div>
  );
}
