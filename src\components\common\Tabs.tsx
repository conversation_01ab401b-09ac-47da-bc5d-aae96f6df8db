import React, { useState, useRef, useEffect } from "react";

type Tab = {
  label: string;
  value: string;
  content?: React.ReactNode;
};

type TabsProps = {
  tabs: Tab[];
  activeTab: string;
  setActiveTab: (tab: Tab) => void;
  handleRemoveSelectedEvents?: () => void;
  sessionCount: number;
};

const Tabs: React.FC<TabsProps> = ({
  tabs,
  activeTab,
  setActiveTab,
  handleRemoveSelectedEvents,
  sessionCount,
}) => {
  const [indicatorStyle, setIndicatorStyle] = useState<React.CSSProperties>({});
  const tabRefs = useRef<(HTMLLIElement | null)[]>([]); // Properly typing as HTMLLIElement

  const handleTabClick = (tab: Tab, index: number) => {
    setActiveTab(tab);
    handleRemoveSelectedEvents?.();
    moveIndicator(index);
  };

  const moveIndicator = (index: number) => {
    const currentTab = tabRefs.current[index];
    if (currentTab) {
      setIndicatorStyle({
        width: currentTab.offsetWidth,
        left: currentTab.offsetLeft,
      });
    }
  };

  useEffect(() => {
    const currentIndex = tabs.findIndex((tab) => tab.label === activeTab);
    moveIndicator(currentIndex >= 0 ? currentIndex : 0); // Update indicator for the active tab
  }, [activeTab, tabs]);

  return (
    <div>
      <ul className="relative flex items-center md:gap-0 gap-2 flex-wrap text-base/4">
        {tabs.map((tab, index) => (
          <li
            key={tab.label}
            ref={(el) => {
              tabRefs.current[index] = el; // Ensure no return value
            }}
            onClick={() => handleTabClick(tab, index)}
            className={`cursor-pointer sm:px-4 sm:py-4.5 p-2 transition-colors duration-300 ${
              activeTab === tab.label
                ? "text-green-600 font-medium"
                : "text-primary/50"
            }`}
          >
            {tab.label} {activeTab === tab.label && `(${sessionCount})`}{" "}
            {/* Example usage */}
          </li>
        ))}
        {/* Sliding border indicator */}
        <span
          className="absolute bottom-0 h-[1.5px] bg-green-600 transition-all duration-300 hidden sm:block"
          style={indicatorStyle}
        />
      </ul>
      <div className="overflow-hidden">
        <div
          className={`transition-opacity duration-300 ease-in-out ${
            activeTab ? "opacity-100" : "opacity-0"
          }`}
        >
          {tabs.find((tab) => tab.label === activeTab)?.content}
        </div>
      </div>
    </div>
  );
};

export default Tabs;
