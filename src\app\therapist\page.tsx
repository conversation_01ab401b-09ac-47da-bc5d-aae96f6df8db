"use client";
import AutoScrollCarousel from "../Landing Page/CommunityPage";
import FaqSection from "../Landing Page/Faq";
import HeroSection from "../Landing Page/HeroSection";
import HowItWorksSection from "../Landing Page/HowWorks";
import NewsletterSection from "../Landing Page/NewsLetter";
import WhyThoughtPudding from "../Landing Page/WTPSection";
import WebsiteLayout from "@/layout/website/WebsiteLayout";

export default function TherapistLandingPage() {

  const isIOS = () => {
    if (typeof window !== "undefined") {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
             (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    }
    return false;
  };
  
  const backgroundStyles = {
    backgroundImage: "url('/assets/images/newHome/bg-home.png')",
    backgroundSize: isIOS() ? "contain" : "cover",
    backgroundPosition: "center",
    backgroundAttachment: isIOS() ? "scroll" : "fixed",
  };
  

  return (
    <WebsiteLayout>
      <div
        className= {`min-h-screen  ${isIOS() ? "" : "bg-cover bg-center bg-no-repeat"}`}
        style={backgroundStyles}
      >
        <div className="pt-[50px] lg:px-[150px] lg:py-[100px] md:px-[75px] md:py-[60px] px-4 py-2">
          <HeroSection />
          <WhyThoughtPudding />
        </div>

        <AutoScrollCarousel />

        <div className="lg:px-[150px] lg:py-[40px] md:px-[75px] md:py-[20px] px-4 py-2">
          <HowItWorksSection />
          <FaqSection />
        </div>

        <NewsletterSection />
      </div>
    </WebsiteLayout>
  );
}
